#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Módulo de traducción para la aplicación
"""

import importlib
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from database import Setting

# Variable global para almacenar las traducciones actuales
current_translations = {}

def load_translations(language_code=None):
    """
    Carga las traducciones para el idioma especificado o el idioma predeterminado de la base de datos
    """
    global current_translations
    
    # Si no se especifica un idioma, obtener el idioma predeterminado de la base de datos
    if language_code is None:
        try:
            engine = create_engine('sqlite:///diamond_sales.db')
            Session = sessionmaker(bind=engine)
            session = Session()
            
            setting = session.query(Setting).first()
            if setting:
                language_code = setting.language
            else:
                language_code = 'ar'  # Árabe por defecto
                
            session.close()
        except Exception:
            language_code = 'ar'  # Si hay un error, usar árabe por defecto
    
    # Intentar cargar el módulo de traducción
    try:
        translation_module = importlib.import_module(f'translations.{language_code}')
        current_translations = translation_module.translations
    except (ImportError, AttributeError):
        # Si no se puede cargar el módulo, intentar cargar el árabe como respaldo
        try:
            translation_module = importlib.import_module('translations.ar')
            current_translations = translation_module.translations
        except (ImportError, AttributeError):
            # Si todo falla, usar un diccionario vacío
            current_translations = {}
    
    return current_translations

def get_translation(key, default=None):
    """
    Obtiene la traducción para la clave especificada
    """
    global current_translations
    
    # Si el diccionario de traducciones está vacío, cargarlo
    if not current_translations:
        load_translations()
    
    # Devolver la traducción o el valor predeterminado si no existe
    return current_translations.get(key, default if default is not None else key)

def change_language(language_code):
    """
    Cambia el idioma actual y lo guarda en la base de datos
    """
    try:
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()
        
        setting = session.query(Setting).first()
        if setting:
            setting.language = language_code
            session.commit()
        
        session.close()
        
        # Cargar las nuevas traducciones
        load_translations(language_code)
        
        return True
    except Exception as e:
        print(f"Error al cambiar el idioma: {str(e)}")
        return False

# Cargar las traducciones al importar el módulo
load_translations()

# Alias para get_translation para facilitar su uso
_ = get_translation
