from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, <PERSON><PERSON><PERSON>, <PERSON>ole<PERSON>, Index, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime, timedelta
import bcrypt
import re
import secrets
import hashlib
import sqlite3
from logger import log_error, log_info

Base = declarative_base()

# تعريف دالة للتحقق من قوة كلمة المرور
def is_strong_password(password):
    """
    التحقق من قوة كلمة المرور

    يجب أن تحتوي كلمة المرور على:
    - حرف واحد على الأقل (يمكن أن يكون حرفًا أو رقمًا)

    Args:
        password (str): كلمة المرور للتحقق منها

    Returns:
        bool: True إذا كانت كلمة المرور قوية، False خلاف ذلك
    """
    # التحقق من أن كلمة المرور غير فارغة
    if not password:
        return False

    # التحقق من وجود حرف أو رقم واحد على الأقل
    if not re.search(r'[A-Za-z0-9]', password):
        return False

    return True

# دالة لتشفير كلمة المرور
def hash_password(password):
    """
    تشفير كلمة المرور باستخدام bcrypt

    Args:
        password (str): كلمة المرور للتشفير

    Returns:
        str: كلمة المرور المشفرة
    """
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

# دالة للتحقق من كلمة المرور
def check_password(hashed_password, password):
    """
    التحقق من كلمة المرور

    Args:
        hashed_password (str): كلمة المرور المشفرة
        password (str): كلمة المرور للتحقق منها

    Returns:
        bool: True إذا كانت كلمة المرور صحيحة، False خلاف ذلك
    """
    try:
        return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
    except Exception as e:
        log_error(f"خطأ في التحقق من كلمة المرور: {str(e)}")
        return False

# تعريف نموذج صندوق النقدية
class صندوق_النقدية(Base):
    __tablename__ = 'cash_box'

    id = Column(Integer, primary_key=True)
    balance = Column(Float, default=0.0, nullable=False)
    last_updated = Column(DateTime, default=datetime.now, nullable=False)

    # العلاقات
    transactions = relationship("حركة_نقدية", back_populates="cash_box")

    def __repr__(self):
        return f"<صندوق_النقدية(id={self.id}, balance={self.balance})>"

# تعريف نموذج حركات النقدية
class حركة_نقدية(Base):
    __tablename__ = 'cash_transactions'

    id = Column(Integer, primary_key=True)
    cash_box_id = Column(Integer, ForeignKey('cash_box.id', ondelete='CASCADE'), nullable=True)  # جعلناه اختياري للسماح بالسندات
    transaction_type = Column(String(20), nullable=False)  # deposit أو withdraw
    amount = Column(Float, nullable=False)
    balance_after = Column(Float, nullable=False)
    transaction_date = Column(DateTime, default=datetime.now, nullable=False)
    reference = Column(String(100), nullable=True)  # المرجع أو رقم المستند
    description = Column(String(500), nullable=True)
    created_by = Column(Integer, ForeignKey('users.id'), nullable=True)
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    receipt_id = Column(Integer, nullable=True)  # معرف السند المرتبط

    # العلاقات
    cash_box = relationship("صندوق_النقدية", back_populates="transactions")
    user = relationship("User", foreign_keys=[created_by])

    # إنشاء فهارس للبحث السريع
    __table_args__ = (
        Index('idx_cash_trans_date', 'transaction_date'),
        Index('idx_cash_trans_type', 'transaction_type'),
    )

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # إضافة خصائص إضافية للاستخدام المؤقت (لا تخزن في قاعدة البيانات)
        self.is_receipt = kwargs.get('is_receipt', False)
        self.calculated_balance = kwargs.get('calculated_balance', 0)

    def __repr__(self):
        return f"<حركة_نقدية(id={self.id}, type={self.transaction_type}, amount={self.amount})>"

class CompanyInfo(Base):
    __tablename__ = 'company_info'
    id = Column(Integer, primary_key=True)
    name = Column(String(100))
    address = Column(String(200))
    tax_number = Column(String(50))
    logo_path = Column(String(200))

class ChartOfAccounts(Base):
    __tablename__ = 'chart_of_accounts'
    id = Column(Integer, primary_key=True)
    name = Column(String(100))
    account_type = Column(String(50))
    parent_account_id = Column(Integer, ForeignKey('chart_of_accounts.id'))

# Definición de las nuevas tablas para categorías y unidades
class Category(Base):
    __tablename__ = 'categories'
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(String(200))
    # Relaciones
    units = relationship("Unit", back_populates="category")
    sales = relationship("Sale", back_populates="category")
    purchases = relationship("Purchase", back_populates="category")

class Unit(Base):
    __tablename__ = 'units'
    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False)
    symbol = Column(String(10))
    description = Column(String(200))
    # Relación con categoría
    category_id = Column(Integer, ForeignKey('categories.id'), nullable=True)
    # Relaciones
    category = relationship("Category", back_populates="units")
    sales = relationship("Sale", back_populates="unit")
    purchases = relationship("Purchase", back_populates="unit")

class Customer(Base):
    __tablename__ = 'customers'
    id = Column(Integer, primary_key=True)
    name = Column(String(100))
    id_number = Column(String(50))
    phone = Column(String(20))
    address = Column(String(200))
    sales = relationship("Sale", back_populates="customer")
    receipts = relationship("Receipt", foreign_keys="Receipt.customer_id")

class Supplier(Base):
    __tablename__ = 'suppliers'
    id = Column(Integer, primary_key=True)
    name = Column(String(100))
    id_number = Column(String(50))
    phone = Column(String(20))
    address = Column(String(200))
    purchases = relationship("Purchase", back_populates="supplier")
    receipts = relationship("Receipt", foreign_keys="Receipt.supplier_id")

class Sale(Base):
    __tablename__ = 'sales'
    id = Column(Integer, primary_key=True)
    customer_id = Column(Integer, ForeignKey('customers.id'))
    diamond_type = Column(String(50))
    carat_weight = Column(Float)
    price_per_carat_usd = Column(Float)
    total_price_usd = Column(Float)
    total_price_sar = Column(Float)
    sale_date = Column(DateTime, default=datetime.now)
    exchange_rate = Column(Float)
    amount_due = Column(Float)
    amount_paid = Column(Float)
    notes = Column(String(500), nullable=True)  # Nuevo campo para notas/observaciones
    # Nuevos campos para categoría y unidad
    category_id = Column(Integer, ForeignKey('categories.id'), nullable=True)
    unit_id = Column(Integer, ForeignKey('units.id'), nullable=True)
    # Relaciones
    customer = relationship("Customer", back_populates="sales")
    category = relationship("Category", back_populates="sales")
    unit = relationship("Unit", back_populates="sales")

class Purchase(Base):
    __tablename__ = 'purchases'
    id = Column(Integer, primary_key=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'))
    diamond_type = Column(String(50))
    carat_weight = Column(Float)
    price_per_carat_usd = Column(Float)
    total_price_usd = Column(Float)
    total_price_sar = Column(Float)
    purchase_date = Column(DateTime, default=datetime.now)
    exchange_rate = Column(Float)
    amount_due = Column(Float)
    amount_paid = Column(Float)
    # Nuevos campos para categoría y unidad
    category_id = Column(Integer, ForeignKey('categories.id'), nullable=True)
    unit_id = Column(Integer, ForeignKey('units.id'), nullable=True)
    # Relaciones
    supplier = relationship("Supplier", back_populates="purchases")
    category = relationship("Category", back_populates="purchases")
    unit = relationship("Unit", back_populates="purchases")

class Receipt(Base):
    __tablename__ = 'receipts'
    id = Column(Integer, primary_key=True)
    sale_id = Column(Integer, ForeignKey('sales.id'))
    purchase_id = Column(Integer, ForeignKey('purchases.id'))
    customer_id = Column(Integer, ForeignKey('customers.id'))  # إضافة حقل معرف العميل
    supplier_id = Column(Integer, ForeignKey('suppliers.id'))  # إضافة حقل معرف المورد
    receipt_type = Column(String(20))
    amount_usd = Column(Float)
    amount_sar = Column(Float)
    issue_date = Column(DateTime, default=datetime.now)

class JournalEntry(Base):
    __tablename__ = 'journal_entries'
    id = Column(Integer, primary_key=True)
    account_id = Column(Integer, ForeignKey('chart_of_accounts.id'))
    debit = Column(Float)
    credit = Column(Float)
    date = Column(DateTime, default=datetime.now)
    description = Column(String(200))
    sale_id = Column(Integer, ForeignKey('sales.id'))
    purchase_id = Column(Integer, ForeignKey('purchases.id'))
    receipt_id = Column(Integer, ForeignKey('receipts.id'))

class User(Base):
    __tablename__ = 'users'
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(100), nullable=False)
    role = Column(String(20), nullable=False)
    full_name = Column(String(100), nullable=True)
    email = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    is_active = Column(Boolean, default=True)
    last_login = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, nullable=False)
    failed_login_attempts = Column(Integer, default=0)
    remember_token = Column(String(100), nullable=True)
    reset_password_token = Column(String(100), nullable=True)
    reset_password_expires = Column(DateTime, nullable=True)

    # إنشاء فهرس على اسم المستخدم للبحث السريع
    __table_args__ = (
        Index('idx_username', 'username'),
        Index('idx_reset_token', 'reset_password_token'),
    )

    def set_password(self, password):
        """
        تعيين كلمة المرور للمستخدم مع التحقق من قوتها

        Args:
            password (str): كلمة المرور الجديدة

        Returns:
            bool: True إذا تم تعيين كلمة المرور بنجاح، False خلاف ذلك

        Raises:
            ValueError: إذا كانت كلمة المرور ضعيفة
        """
        if not is_strong_password(password):
            raise ValueError("كلمة المرور ضعيفة. يجب أن تحتوي على حرف أو رقم واحد على الأقل.")

        self.password_hash = hash_password(password)
        return True

    def check_password(self, password):
        """
        التحقق من كلمة المرور

        Args:
            password (str): كلمة المرور للتحقق منها

        Returns:
            bool: True إذا كانت كلمة المرور صحيحة، False خلاف ذلك
        """
        return check_password(self.password_hash, password)

    def update_last_login(self):
        """
        تحديث وقت آخر تسجيل دخول
        """
        self.last_login = datetime.now()
        self.failed_login_attempts = 0

    def increment_failed_login(self):
        """
        زيادة عدد محاولات تسجيل الدخول الفاشلة
        """
        self.failed_login_attempts += 1

    def generate_remember_token(self):
        """
        توليد رمز تذكر كلمة المرور

        Returns:
            str: رمز تذكر كلمة المرور
        """
        token = secrets.token_hex(32)
        self.remember_token = token
        return token

    def clear_remember_token(self):
        """
        مسح رمز تذكر كلمة المرور
        """
        self.remember_token = None



    def generate_reset_token(self, expires_in=3600):
        """
        توليد رمز إعادة تعيين كلمة المرور

        Args:
            expires_in (int): مدة صلاحية الرمز بالثواني (الافتراضي: ساعة واحدة)

        Returns:
            str: رمز إعادة تعيين كلمة المرور
        """
        token = secrets.token_hex(16)
        self.reset_password_token = token
        self.reset_password_expires = datetime.now() + timedelta(seconds=expires_in)
        return token



    def verify_reset_token(self, token):
        """
        التحقق من صحة رمز إعادة تعيين كلمة المرور

        Args:
            token (str): رمز إعادة تعيين كلمة المرور

        Returns:
            bool: True إذا كان الرمز صحيحاً وغير منتهي الصلاحية، False خلاف ذلك
        """
        if not self.reset_password_token or self.reset_password_token != token:
            return False

        if not self.reset_password_expires or datetime.now() > self.reset_password_expires:
            return False

        return True

    def clear_reset_token(self):
        """
        مسح رمز إعادة تعيين كلمة المرور
        """
        self.reset_password_token = None
        self.reset_password_expires = None

class Setting(Base):
    __tablename__ = 'settings'
    id = Column(Integer, primary_key=True)
    exchange_rate = Column(Float, default=3.75)
    default_currency = Column(String(3), default='SAR')
    language = Column(String(2), default='ar')
    backup_path = Column(String(200), nullable=True)
    last_backup_date = Column(DateTime, nullable=True)
    auto_backup = Column(Boolean, default=True)
    font_family = Column(String(100), default='Arial')
    font_size = Column(Integer, default=10)

class Payment(Base):
    __tablename__ = 'payments'
    id = Column(Integer, primary_key=True)
    sale_id = Column(Integer, ForeignKey('sales.id'), nullable=True)
    purchase_id = Column(Integer, ForeignKey('purchases.id'), nullable=True)
    amount_usd = Column(Float)
    amount_sar = Column(Float)
    payment_date = Column(DateTime, default=datetime.now)
    payment_method = Column(String(50))
    reference = Column(String(100))
    description = Column(String(200))

class Permission(Base):
    __tablename__ = 'permissions'
    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(String(200))
    code = Column(String(50), unique=True, nullable=False)

    def __repr__(self):
        return f"<Permission(name='{self.name}', code='{self.code}')>"

class RolePermission(Base):
    __tablename__ = 'role_permissions'
    id = Column(Integer, primary_key=True)
    role = Column(String(20), nullable=False)
    permission_id = Column(Integer, ForeignKey('permissions.id'), nullable=False)

    # إنشاء فهرس مركب للتأكد من عدم تكرار الأدوار والصلاحيات
    __table_args__ = (
        UniqueConstraint('role', 'permission_id', name='uq_role_permission'),
    )

    def __repr__(self):
        return f"<RolePermission(role='{self.role}', permission_id={self.permission_id})>"

class Activation(Base):
    __tablename__ = 'activation'
    id = Column(Integer, primary_key=True)
    activation_code = Column(String(200), nullable=False)
    activation_date = Column(DateTime, default=datetime.now)
    expiry_date = Column(DateTime, nullable=True)
    hardware_id = Column(String(50), nullable=False)
    features = Column(String(200), nullable=True)  # Lista de características separadas por comas
    default_expiry_days = Column(Integer, default=365)  # Duración predeterminada para nuevos códigos
    notify_before_days = Column(Integer, default=30)  # Notificar antes de X días de la expiración

    def __repr__(self):
        return f"<Activation(id={self.id}, activation_date='{self.activation_date}', expiry_date='{self.expiry_date}')>"

class DataLock(Base):
    __tablename__ = 'data_locks'
    id = Column(Integer, primary_key=True)
    lock_period_type = Column(String(20))  # شهر / ربع سنوي / نصف سنوي / سنوي
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    is_locked = Column(Boolean, default=False)
    created_by = Column(Integer, ForeignKey('users.id'))
    created_date = Column(DateTime, default=datetime.now)
    locked_by_user_id = Column(Integer, ForeignKey('users.id'), nullable=True)

def import_database(old_db_path):
    """
    استيراد البيانات من قاعدة بيانات قديمة وتحديثها لتتوافق مع الهيكل الجديد

    Args:
        old_db_path (str): مسار قاعدة البيانات القديمة

    Returns:
        bool: True إذا تم الاستيراد بنجاح، False خلاف ذلك
    """
    try:
        # إنشاء محرك قاعدة البيانات للقاعدة القديمة
        old_engine = create_engine(f'sqlite:///{old_db_path}')
        Old_Session = sessionmaker(bind=old_engine)
        old_session = Old_Session()

        # إنشاء محرك قاعدة البيانات للقاعدة الجديدة
        new_engine = create_engine('sqlite:///diamond_sales.db')
        New_Session = sessionmaker(bind=new_engine)
        new_session = New_Session()

        log_info(f"بدء استيراد البيانات من {old_db_path}")

        try:
            # استيراد العملاء
            log_info("استيراد بيانات العملاء")
            old_customers = old_session.execute("SELECT * FROM customers").fetchall()
            for old_customer in old_customers:
                # التحقق من وجود العميل في القاعدة الجديدة
                customer = new_session.query(Customer).filter_by(id=old_customer.id).first()
                if not customer:
                    customer = Customer(
                        id=old_customer.id,
                        name=old_customer.name,
                        id_number=old_customer.id_number,
                        phone=old_customer.phone,
                        address=old_customer.address
                    )
                    new_session.add(customer)

            # استيراد الموردين
            log_info("استيراد بيانات الموردين")
            old_suppliers = old_session.execute("SELECT * FROM suppliers").fetchall()
            for old_supplier in old_suppliers:
                # التحقق من وجود المورد في القاعدة الجديدة
                supplier = new_session.query(Supplier).filter_by(id=old_supplier.id).first()
                if not supplier:
                    supplier = Supplier(
                        id=old_supplier.id,
                        name=old_supplier.name,
                        id_number=old_supplier.id_number,
                        phone=old_supplier.phone,
                        address=old_supplier.address
                    )
                    new_session.add(supplier)

            # استيراد المبيعات
            log_info("استيراد بيانات المبيعات")
            old_sales = old_session.execute("SELECT * FROM sales").fetchall()
            for old_sale in old_sales:
                # التحقق من وجود المبيعات في القاعدة الجديدة
                sale = new_session.query(Sale).filter_by(id=old_sale.id).first()
                if not sale:
                    sale = Sale(
                        id=old_sale.id,
                        customer_id=old_sale.customer_id,
                        diamond_type=old_sale.diamond_type,
                        carat_weight=old_sale.carat_weight,
                        price_per_carat_usd=old_sale.price_per_carat_usd,
                        total_price_usd=old_sale.total_price_usd,
                        total_price_sar=old_sale.total_price_sar,
                        sale_date=old_sale.sale_date,
                        exchange_rate=old_sale.exchange_rate,
                        amount_due=old_sale.amount_due,
                        amount_paid=old_sale.amount_paid,
                        notes=old_sale.notes if hasattr(old_sale, 'notes') else None
                    )
                    new_session.add(sale)

            # استيراد المشتريات
            log_info("استيراد بيانات المشتريات")
            old_purchases = old_session.execute("SELECT * FROM purchases").fetchall()
            for old_purchase in old_purchases:
                # التحقق من وجود المشتريات في القاعدة الجديدة
                purchase = new_session.query(Purchase).filter_by(id=old_purchase.id).first()
                if not purchase:
                    purchase = Purchase(
                        id=old_purchase.id,
                        supplier_id=old_purchase.supplier_id,
                        diamond_type=old_purchase.diamond_type,
                        carat_weight=old_purchase.carat_weight,
                        price_per_carat_usd=old_purchase.price_per_carat_usd,
                        total_price_usd=old_purchase.total_price_usd,
                        total_price_sar=old_purchase.total_price_sar,
                        purchase_date=old_purchase.purchase_date,
                        exchange_rate=old_purchase.exchange_rate,
                        amount_due=old_purchase.amount_due,
                        amount_paid=old_purchase.amount_paid
                    )
                    new_session.add(purchase)

            # استيراد سندات القبض والصرف
            log_info("استيراد بيانات سندات القبض والصرف")
            old_receipts = old_session.execute("SELECT * FROM receipts").fetchall()
            for old_receipt in old_receipts:
                # التحقق من وجود السند في القاعدة الجديدة
                receipt = new_session.query(Receipt).filter_by(id=old_receipt.id).first()
                if not receipt:
                    # تحديد معرف العميل أو المورد
                    customer_id = None
                    supplier_id = None

                    if old_receipt.receipt_type == "CashIn":
                        if old_receipt.sale_id:
                            # الحصول على معرف العميل من المبيعات
                            sale = new_session.query(Sale).filter_by(id=old_receipt.sale_id).first()
                            if sale:
                                customer_id = sale.customer_id

                    elif old_receipt.receipt_type == "CashOut":
                        if old_receipt.purchase_id:
                            # الحصول على معرف المورد من المشتريات
                            purchase = new_session.query(Purchase).filter_by(id=old_receipt.purchase_id).first()
                            if purchase:
                                supplier_id = purchase.supplier_id

                    receipt = Receipt(
                        id=old_receipt.id,
                        sale_id=old_receipt.sale_id,
                        purchase_id=old_receipt.purchase_id,
                        customer_id=customer_id,
                        supplier_id=supplier_id,
                        receipt_type=old_receipt.receipt_type,
                        amount_usd=old_receipt.amount_usd,
                        amount_sar=old_receipt.amount_sar,
                        issue_date=old_receipt.issue_date
                    )
                    new_session.add(receipt)

            # استيراد الإعدادات
            log_info("استيراد الإعدادات")
            old_settings = old_session.execute("SELECT * FROM settings").fetchall()
            if old_settings:
                old_setting = old_settings[0]
                setting = new_session.query(Setting).first()
                if setting:
                    setting.exchange_rate = old_setting.exchange_rate
                    setting.default_currency = old_setting.default_currency
                    setting.language = old_setting.language
                    setting.backup_path = old_setting.backup_path if hasattr(old_setting, 'backup_path') else None
                    setting.last_backup_date = old_setting.last_backup_date if hasattr(old_setting, 'last_backup_date') else None
                    setting.auto_backup = old_setting.auto_backup if hasattr(old_setting, 'auto_backup') else True
                    setting.font_family = old_setting.font_family if hasattr(old_setting, 'font_family') else 'Arial'
                    setting.font_size = old_setting.font_size if hasattr(old_setting, 'font_size') else 10

            # استيراد المستخدمين
            log_info("استيراد بيانات المستخدمين")
            old_users = old_session.execute("SELECT * FROM users").fetchall()
            for old_user in old_users:
                # التحقق من وجود المستخدم في القاعدة الجديدة
                user = new_session.query(User).filter_by(username=old_user.username).first()
                if not user:
                    user = User(
                        username=old_user.username,
                        password_hash=old_user.password_hash,
                        role=old_user.role,
                        full_name=old_user.full_name if hasattr(old_user, 'full_name') else None,
                        email=old_user.email if hasattr(old_user, 'email') else None,
                        phone=old_user.phone if hasattr(old_user, 'phone') else None,
                        is_active=old_user.is_active if hasattr(old_user, 'is_active') else True,
                        last_login=old_user.last_login if hasattr(old_user, 'last_login') else None,
                        created_at=old_user.created_at if hasattr(old_user, 'created_at') else datetime.now(),
                        updated_at=old_user.updated_at if hasattr(old_user, 'updated_at') else datetime.now()
                    )
                    new_session.add(user)

            # حفظ التغييرات
            new_session.commit()
            log_info("تم استيراد البيانات بنجاح")
            return True

        except Exception as e:
            new_session.rollback()
            log_error(f"خطأ في استيراد البيانات: {str(e)}")
            return False
        finally:
            old_session.close()
            new_session.close()

    except Exception as e:
        log_error(f"خطأ في فتح قاعدة البيانات: {str(e)}")
        return False

def update_db_schema():
    """
    تحديث هيكل قاعدة البيانات الحالية لإضافة الأعمدة الجديدة
    """
    try:
        # إنشاء اتصال بقاعدة البيانات
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()

        # التحقق من وجود عمود customer_id في جدول receipts
        cursor.execute("PRAGMA table_info(receipts)")
        columns = [column[1] for column in cursor.fetchall()]

        # إضافة الأعمدة الجديدة إذا لم تكن موجودة
        if 'customer_id' not in columns:
            log_info("إضافة عمود customer_id إلى جدول receipts")
            cursor.execute("ALTER TABLE receipts ADD COLUMN customer_id INTEGER REFERENCES customers(id)")

        if 'supplier_id' not in columns:
            log_info("إضافة عمود supplier_id إلى جدول receipts")
            cursor.execute("ALTER TABLE receipts ADD COLUMN supplier_id INTEGER REFERENCES suppliers(id)")

        # تحديث قيم customer_id و supplier_id للسندات الموجودة
        log_info("تحديث قيم customer_id و supplier_id للسندات الموجودة")

        # تحديث سندات القبض المرتبطة بمبيعات
        cursor.execute("""
            UPDATE receipts
            SET customer_id = (
                SELECT customer_id FROM sales WHERE sales.id = receipts.sale_id
            )
            WHERE receipt_type = 'CashIn' AND sale_id IS NOT NULL
        """)

        # تحديث سندات الصرف المرتبطة بمشتريات
        cursor.execute("""
            UPDATE receipts
            SET supplier_id = (
                SELECT supplier_id FROM purchases WHERE purchases.id = receipts.purchase_id
            )
            WHERE receipt_type = 'CashOut' AND purchase_id IS NOT NULL
        """)

        # حفظ التغييرات
        conn.commit()
        conn.close()
        log_info("تم تحديث هيكل قاعدة البيانات بنجاح")

    except Exception as e:
        log_error(f"خطأ في تحديث هيكل قاعدة البيانات: {str(e)}")
        raise

def init_db():
    """
    تهيئة قاعدة البيانات وإنشاء الجداول والبيانات الأولية
    """
    try:
        # إنشاء محرك قاعدة البيانات
        engine = create_engine('sqlite:///diamond_sales.db')

        # إنشاء الجداول
        Base.metadata.create_all(engine)

        # تحديث هيكل قاعدة البيانات إذا كانت موجودة بالفعل
        update_db_schema()

        # إنشاء جلسة
        Session = sessionmaker(bind=engine)
        session = Session()        # إضافة البيانات الأولية
        try:
            # التحقق من وجود الإعدادات
            if not session.query(Setting).first():
                log_info("إنشاء إعدادات افتراضية جديدة")
                setting = Setting()
                session.add(setting)            # التحقق من وجود صندوق النقدية
            if not session.query(صندوق_النقدية).first():
                log_info("إنشاء صندوق النقدية الافتراضي")
                cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                session.add(cash_box)

            # التحقق من وجود المستخدم الإداري
            admin_user = session.query(User).filter_by(username='admin').first()
            if not admin_user:
                log_info("إنشاء حساب المسؤول الافتراضي")

                # إنشاء كلمة مرور قوية للمسؤول
                admin_password = 'Admin@123'  # كلمة مرور قوية افتراضية

                # إنشاء المستخدم الإداري
                admin = User(
                    username='admin',
                    password_hash=hash_password(admin_password),
                    role='admin',
                    full_name='مدير النظام',
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                session.add(admin)            # إضافة الصلاحيات الافتراضية
            default_permissions = [
                {"name": "إدارة المستخدمين", "code": "manage_users", "description": "إضافة وتعديل وحذف المستخدمين"},
                {"name": "إدارة العملاء", "code": "manage_customers", "description": "إضافة وتعديل وحذف العملاء"},
                {"name": "إدارة الموردين", "code": "manage_suppliers", "description": "إضافة وتعديل وحذف الموردين"},
                {"name": "إدارة المبيعات", "code": "manage_sales", "description": "إضافة وتعديل وحذف المبيعات"},
                {"name": "إدارة المشتريات", "code": "manage_purchases", "description": "إضافة وتعديل وحذف المشتريات"},
                {"name": "إدارة التقارير", "code": "manage_reports", "description": "عرض وطباعة التقارير"},
                {"name": "إدارة الإعدادات", "code": "manage_settings", "description": "تعديل إعدادات النظام"},
                {"name": "إدارة النسخ الاحتياطي", "code": "manage_backup", "description": "إنشاء واستعادة النسخ الاحتياطي"},
                {"name": "إقفال البيانات", "code": "lock_data", "description": "إقفال البيانات لفترة محددة"},
                {"name": "إدارة الصلاحيات", "code": "manage_permissions", "description": "إدارة صلاحيات المستخدمين"},                {"name": "إدارة صندوق النقدية", "code": "ادارة_صندوق_النقدية", "description": "إدارة صندوق النقدية والإيداعات والمسحوبات"},
                {"name": "عرض صندوق النقدية", "code": "عرض_صندوق_النقدية", "description": "عرض حركات وأرصدة صندوق النقدية"}
            ]

            for perm_data in default_permissions:
                # التحقق من وجود الصلاحية
                perm = session.query(Permission).filter_by(code=perm_data["code"]).first()
                if not perm:
                    log_info(f"إنشاء صلاحية: {perm_data['name']}")
                    perm = Permission(
                        name=perm_data["name"],
                        code=perm_data["code"],
                        description=perm_data["description"]
                    )
                    session.add(perm)

            # إضافة جميع الصلاحيات لدور المسؤول
            session.flush()  # لضمان أن جميع الصلاحيات لها معرفات

            # الحصول على جميع الصلاحيات
            all_permissions = session.query(Permission).all()

            # إضافة جميع الصلاحيات لدور المسؤول
            for perm in all_permissions:
                # التحقق من وجود الصلاحية للدور
                role_perm = session.query(RolePermission).filter_by(
                    role="admin",
                    permission_id=perm.id
                ).first()

                if not role_perm:
                    log_info(f"إضافة صلاحية '{perm.name}' لدور المسؤول")
                    role_perm = RolePermission(
                        role="admin",
                        permission_id=perm.id
                    )
                    session.add(role_perm)

            # إضافة صلاحيات محدودة للأدوار الأخرى
            role_permissions = {
                "user": ["manage_customers", "manage_sales", "manage_reports", "عرض_صندوق_النقدية"],
                "accountant": ["manage_customers", "manage_suppliers", "manage_sales", "manage_purchases", "manage_reports", "ادارة_صندوق_النقدية", "عرض_صندوق_النقدية"],
                "sales": ["manage_customers", "manage_sales", "manage_reports", "عرض_صندوق_النقدية"],
                "manager": ["manage_customers", "manage_suppliers", "manage_sales", "manage_purchases", "manage_reports", "manage_users", "ادارة_صندوق_النقدية", "عرض_صندوق_النقدية"]
            }

            for role, perm_codes in role_permissions.items():
                for code in perm_codes:
                    perm = session.query(Permission).filter_by(code=code).first()
                    if perm:
                        # التحقق من وجود الصلاحية للدور
                        role_perm = session.query(RolePermission).filter_by(
                            role=role,
                            permission_id=perm.id
                        ).first()

                        if not role_perm:
                            log_info(f"إضافة صلاحية '{perm.name}' لدور {role}")
                            role_perm = RolePermission(
                                role=role,
                                permission_id=perm.id
                            )
                            session.add(role_perm)

            # إضافة فهارس للجداول المستخدمة بكثرة
            log_info("إنشاء فهارس لتحسين الأداء")

            session.commit()
            log_info("تم تهيئة قاعدة البيانات بنجاح")
        except Exception as e:
            session.rollback()
            log_error("خطأ في تهيئة قاعدة البيانات", e)
            raise
        finally:
            session.close()
    except Exception as e:
        log_error("خطأ في إنشاء قاعدة البيانات", e)
        raise

def update_db_schema():
    """
    تحديث هيكل قاعدة البيانات الحالية لإضافة الأعمدة الجديدة
    """
    try:
        # إنشاء اتصال بقاعدة البيانات
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()

        # التحقق من وجود جدول cash_box
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cash_box'")
        if not cursor.fetchone():
            log_info("إنشاء جدول cash_box")
            cursor.execute("""
                CREATE TABLE cash_box (
                    id INTEGER PRIMARY KEY,
                    balance REAL NOT NULL DEFAULT 0.0,
                    last_updated TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # إضافة سجل أولي في صندوق النقدية
            cursor.execute("INSERT INTO cash_box (balance, last_updated) VALUES (0.0, CURRENT_TIMESTAMP)")

        # التحقق من وجود جدول cash_transactions
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cash_transactions'")
        if not cursor.fetchone():
            log_info("إنشاء جدول cash_transactions")
            cursor.execute("""
                CREATE TABLE cash_transactions (
                    id INTEGER PRIMARY KEY,
                    cash_box_id INTEGER REFERENCES cash_box(id),
                    transaction_type TEXT NOT NULL,
                    amount REAL NOT NULL,
                    balance_after REAL NOT NULL,
                    transaction_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    reference TEXT,
                    description TEXT,
                    created_by INTEGER REFERENCES users(id),
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # إنشاء الفهارس لتحسين الأداء
            cursor.execute("CREATE INDEX idx_cash_trans_date ON cash_transactions (transaction_date)")
            cursor.execute("CREATE INDEX idx_cash_trans_type ON cash_transactions (transaction_type)")

        # التحقق من وجود جدول categories
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='categories'")
        if not cursor.fetchone():
            log_info("إنشاء جدول categories")
            cursor.execute("""
                CREATE TABLE categories (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT
                )
            """)

        # التحقق من وجود جدول units
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='units'")
        if not cursor.fetchone():
            log_info("إنشاء جدول units")
            cursor.execute("""
                CREATE TABLE units (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    symbol TEXT,
                    description TEXT,
                    category_id INTEGER REFERENCES categories(id)
                )
            """)

        # التحقق من وجود أعمدة category_id و unit_id في جدول sales
        cursor.execute("PRAGMA table_info(sales)")
        sales_columns = [column[1] for column in cursor.fetchall()]

        if 'category_id' not in sales_columns:
            log_info("إضافة عمود category_id إلى جدول sales")
            cursor.execute("ALTER TABLE sales ADD COLUMN category_id INTEGER REFERENCES categories(id)")

        if 'unit_id' not in sales_columns:
            log_info("إضافة عمود unit_id إلى جدول sales")
            cursor.execute("ALTER TABLE sales ADD COLUMN unit_id INTEGER REFERENCES units(id)")

        # التحقق من وجود أعمدة category_id و unit_id في جدول purchases
        cursor.execute("PRAGMA table_info(purchases)")
        purchases_columns = [column[1] for column in cursor.fetchall()]

        if 'category_id' not in purchases_columns:
            log_info("إضافة عمود category_id إلى جدول purchases")
            cursor.execute("ALTER TABLE purchases ADD COLUMN category_id INTEGER REFERENCES categories(id)")

        if 'unit_id' not in purchases_columns:
            log_info("إضافة عمود unit_id إلى جدول purchases")
            cursor.execute("ALTER TABLE purchases ADD COLUMN unit_id INTEGER REFERENCES units(id)")

        # التحقق من وجود عمود category_id في جدول units
        cursor.execute("PRAGMA table_info(units)")
        units_columns = [column[1] for column in cursor.fetchall()]

        if 'category_id' not in units_columns:
            log_info("إضافة عمود category_id إلى جدول units")
            cursor.execute("ALTER TABLE units ADD COLUMN category_id INTEGER REFERENCES categories(id)")

        # حفظ التغييرات وإغلاق الاتصال
        conn.commit()
        conn.close()
        log_info("تم تحديث هيكل قاعدة البيانات بنجاح")
        return True
    except Exception as e:
        log_error("خطأ في تحديث هيكل قاعدة البيانات", e)
        return False