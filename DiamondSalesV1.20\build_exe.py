#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لبناء ملف exe من برنامج Diamond Sales
"""

import os
import sys
import shutil
import subprocess
from datetime import datetime

def check_requirements():
    """التحقق من المتطلبات اللازمة لبناء exe"""
    print("🔍 التحقق من المتطلبات...")
    
    # التحقق من وجود PyInstaller
    try:
        import PyInstaller
        print("  ✅ PyInstaller متوفر")
    except ImportError:
        print("  ❌ PyInstaller غير متوفر")
        print("  📦 تثبيت PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("  ✅ تم تثبيت PyInstaller بنجاح")
        except subprocess.CalledProcessError:
            print("  ❌ فشل في تثبيت PyInstaller")
            return False
    
    # التحقق من وجود الملفات الأساسية
    required_files = [
        "main.py",
        "diamond_sales.db",
        "assets",
        "translations"
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file} موجود")
        else:
            print(f"  ❌ {file} غير موجود")
            return False
    
    return True

def create_spec_file():
    """إنشاء ملف .spec لـ PyInstaller"""
    print("📝 إنشاء ملف .spec...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('diamond_sales.db', '.'),
        ('assets', 'assets'),
        ('translations', 'translations'),
        ('logs', 'logs'),
    ],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        'sqlalchemy',
        'bcrypt',
        'PIL',
        'reportlab',
        'openpyxl',
        'xlsxwriter'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='DiamondSales',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/diamond_icon.ico' if os.path.exists('assets/diamond_icon.ico') else None,
)
'''
    
    with open("DiamondSales.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("  ✅ تم إنشاء ملف DiamondSales.spec")
    return True

def prepare_build_environment():
    """تحضير بيئة البناء"""
    print("🔧 تحضير بيئة البناء...")
    
    # إنشاء مجلد logs إذا لم يكن موجوداً
    if not os.path.exists("logs"):
        os.makedirs("logs")
        print("  ✅ تم إنشاء مجلد logs")
    
    # التأكد من وجود ملف __init__.py في مجلد translations
    translations_init = os.path.join("translations", "__init__.py")
    if not os.path.exists(translations_init):
        with open(translations_init, "w", encoding="utf-8") as f:
            f.write("# Translations module\n")
        print("  ✅ تم إنشاء ملف __init__.py في مجلد translations")
    
    return True

def build_exe():
    """بناء ملف exe"""
    print("🔨 بناء ملف exe...")
    
    try:
        # تشغيل PyInstaller
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm", 
            "DiamondSales.spec"
        ]
        
        print("  🔄 تشغيل PyInstaller...")
        print(f"  📝 الأمر: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("  ✅ تم بناء exe بنجاح")
            return True
        else:
            print("  ❌ فشل في بناء exe")
            print("  📋 رسائل الخطأ:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في بناء exe: {str(e)}")
        return False

def copy_additional_files():
    """نسخ الملفات الإضافية إلى مجلد التوزيع"""
    print("📁 نسخ الملفات الإضافية...")
    
    dist_path = "dist/DiamondSales"
    
    if not os.path.exists(dist_path):
        print("  ❌ مجلد التوزيع غير موجود")
        return False
    
    # نسخ قاعدة البيانات النظيفة
    if os.path.exists("diamond_sales.db"):
        shutil.copy2("diamond_sales.db", os.path.join(dist_path, "diamond_sales.db"))
        print("  ✅ تم نسخ قاعدة البيانات")
    
    # نسخ مجلد الأصول
    if os.path.exists("assets"):
        assets_dest = os.path.join(dist_path, "assets")
        if os.path.exists(assets_dest):
            shutil.rmtree(assets_dest)
        shutil.copytree("assets", assets_dest)
        print("  ✅ تم نسخ مجلد الأصول")
    
    # نسخ مجلد الترجمات
    if os.path.exists("translations"):
        translations_dest = os.path.join(dist_path, "translations")
        if os.path.exists(translations_dest):
            shutil.rmtree(translations_dest)
        shutil.copytree("translations", translations_dest)
        print("  ✅ تم نسخ مجلد الترجمات")
    
    # إنشاء مجلد logs فارغ
    logs_dest = os.path.join(dist_path, "logs")
    if not os.path.exists(logs_dest):
        os.makedirs(logs_dest)
        print("  ✅ تم إنشاء مجلد logs")
    
    return True

def create_installer_script():
    """إنشاء سكريبت Inno Setup للمثبت"""
    print("📦 إنشاء سكريبت المثبت...")
    
    installer_script = f'''[Setup]
AppName=Diamond Sales
AppVersion=1.30
AppPublisher=Diamond Sales Company
AppPublisherURL=https://diamondsales.com
AppSupportURL=https://diamondsales.com/support
AppUpdatesURL=https://diamondsales.com/updates
DefaultDirName={{autopf}}\\Diamond Sales
DefaultGroupName=Diamond Sales
AllowNoIcons=yes
LicenseFile=
OutputDir=installer_output
OutputBaseFilename=DiamondSalesSetup_1.30
SetupIconFile=assets\\diamond_icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{{cm:CreateDesktopIcon}}"; GroupDescription: "{{cm:AdditionalIcons}}"; Flags: unchecked

[Files]
Source: "dist\\DiamondSales\\*"; DestDir: "{{app}}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{{group}}\\Diamond Sales"; Filename: "{{app}}\\DiamondSales.exe"
Name: "{{group}}\\{{cm:UninstallProgram,Diamond Sales}}"; Filename: "{{uninstallexe}}"
Name: "{{autodesktop}}\\Diamond Sales"; Filename: "{{app}}\\DiamondSales.exe"; Tasks: desktopicon

[Run]
Filename: "{{app}}\\DiamondSales.exe"; Description: "{{cm:LaunchProgram,Diamond Sales}}"; Flags: nowait postinstall skipifsilent
'''
    
    with open("DiamondSales_installer.iss", "w", encoding="utf-8") as f:
        f.write(installer_script)
    
    print("  ✅ تم إنشاء سكريبت المثبت: DiamondSales_installer.iss")
    return True

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("🧹 تنظيف الملفات المؤقتة...")
    
    temp_dirs = ["build", "__pycache__"]
    temp_files = ["DiamondSales.spec"]
    
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print(f"  ✅ تم حذف مجلد {temp_dir}")
    
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            os.remove(temp_file)
            print(f"  ✅ تم حذف ملف {temp_file}")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏗️  سكريبت بناء ملف exe لبرنامج Diamond Sales")
    print("=" * 60)
    
    # التحقق من المتطلبات
    if not check_requirements():
        print("\n❌ فشل في التحقق من المتطلبات")
        return
    
    # تحضير بيئة البناء
    if not prepare_build_environment():
        print("\n❌ فشل في تحضير بيئة البناء")
        return
    
    # إنشاء ملف .spec
    if not create_spec_file():
        print("\n❌ فشل في إنشاء ملف .spec")
        return
    
    # بناء exe
    if not build_exe():
        print("\n❌ فشل في بناء exe")
        return
    
    # نسخ الملفات الإضافية
    if not copy_additional_files():
        print("\n❌ فشل في نسخ الملفات الإضافية")
        return
    
    # إنشاء سكريبت المثبت
    create_installer_script()
    
    # تنظيف الملفات المؤقتة
    cleanup()
    
    print("\n🎉 تم بناء ملف exe بنجاح!")
    print("📁 الملف موجود في: dist/DiamondSales/DiamondSales.exe")
    print("📦 سكريبت المثبت: DiamondSales_installer.iss")
    print("\n💡 لإنشاء مثبت:")
    print("   1. قم بتثبيت Inno Setup")
    print("   2. افتح ملف DiamondSales_installer.iss")
    print("   3. اضغط F9 لبناء المثبت")

if __name__ == "__main__":
    main()
