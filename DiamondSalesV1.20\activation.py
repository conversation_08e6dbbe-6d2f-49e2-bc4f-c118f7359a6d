"""
وحدة التفعيل
تستخدم هذه الوحدة للتحقق من تفعيل البرنامج وإدارة أكواد التفعيل
"""

import hashlib
import uuid
import base64
import hmac
import re
import platform
import socket
import json
import os
from datetime import datetime, timedelta
from logger import log_error, log_info

# المفتاح السري للتشفير (يجب تغييره في الإنتاج)
SECRET_KEY = "DiamondSales_SecretKey_2025"

def get_hardware_id():
    """
    الحصول على معرف فريد للجهاز

    Returns:
        str: معرف فريد للجهاز
    """
    try:
        # جمع معلومات الجهاز
        system_info = platform.system() + platform.version() + platform.machine()

        # الحصول على عنوان MAC للشبكة
        mac_address = uuid.getnode()

        # الحصول على اسم المضيف
        hostname = socket.gethostname()

        # دمج المعلومات وتشفيرها
        hardware_id = f"{system_info}_{mac_address}_{hostname}"
        hashed_id = hashlib.sha256(hardware_id.encode()).hexdigest()

        # إرجاع أول 16 حرف من المعرف المشفر
        return hashed_id[:16].upper()
    except Exception as e:
        log_error(f"خطأ في الحصول على معرف الجهاز: {str(e)}")
        # إرجاع معرف افتراضي في حالة الخطأ
        return "ERROR_HARDWARE_ID"

def generate_activation_code(hardware_id, expiry_days=365, features=None):
    """
    توليد كود تفعيل للبرنامج

    Args:
        hardware_id (str): معرف الجهاز
        expiry_days (int): عدد أيام صلاحية الكود
        features (list): قائمة بالميزات المفعلة

    Returns:
        str: كود التفعيل
    """
    try:
        # التحقق من صحة معرف الجهاز
        if not hardware_id or len(hardware_id) != 16:
            log_error("معرف الجهاز غير صالح")
            return None

        # إنشاء تاريخ انتهاء الصلاحية
        expiry_date = datetime.now() + timedelta(days=expiry_days)
        expiry_str = expiry_date.strftime("%Y%m%d")

        # إنشاء قائمة الميزات
        if features is None:
            features = ["basic"]
        features_str = ",".join(features)

        # إنشاء بيانات التفعيل المختصرة
        # استخدام أول 4 أحرف من معرف الجهاز
        hw_prefix = hardware_id[:4]
        # استخدام آخر 4 أحرف من معرف الجهاز
        hw_suffix = hardware_id[-4:]
        # تحويل تاريخ انتهاء الصلاحية إلى قيمة عددية (عدد الأيام منذ 2020-01-01)
        base_date = datetime(2020, 1, 1)
        days_diff = (expiry_date - base_date).days
        # تحويل عدد الأيام إلى سلسلة من 4 أحرف
        days_str = format(days_diff, '04x').upper()

        # إنشاء رمز للميزات (حرف واحد لكل ميزة)
        feature_code = ""
        if "basic" in features:
            feature_code += "B"
        if "advanced" in features:
            feature_code += "A"
        if "reports" in features:
            feature_code += "R"
        if "backup" in features:
            feature_code += "K"
        # ملء الرمز بأحرف X حتى يصل إلى 4 أحرف
        feature_code = feature_code.ljust(4, 'X')

        # إنشاء كود التفعيل (16 حرف)
        activation_code_parts = [hw_prefix, days_str, feature_code, hw_suffix]

        # إنشاء توقيع للتحقق من صحة الكود
        raw_code = ''.join(activation_code_parts)
        signature = hmac.new(SECRET_KEY.encode(), raw_code.encode(), hashlib.sha256).hexdigest()[:4].upper()

        # دمج الأجزاء مع التوقيع
        activation_code_parts.insert(2, signature)  # إدراج التوقيع بعد تاريخ انتهاء الصلاحية

        # تنسيق الكود بشكل أفضل (مجموعات من 4 أحرف)
        formatted_code = '-'.join(activation_code_parts)

        return formatted_code
    except Exception as e:
        log_error(f"خطأ في توليد كود التفعيل: {str(e)}")
        return None

def validate_activation_code(activation_code, hardware_id=None):
    """
    التحقق من صحة كود التفعيل

    Args:
        activation_code (str): كود التفعيل
        hardware_id (str): معرف الجهاز (إذا كان None، سيتم الحصول عليه تلقائيًا)

    Returns:
        dict: معلومات التفعيل إذا كان الكود صالحًا، None خلاف ذلك
    """
    try:
        # الحصول على معرف الجهاز إذا لم يتم توفيره
        if hardware_id is None:
            hardware_id = get_hardware_id()

        # التحقق من صحة معرف الجهاز
        if not hardware_id or len(hardware_id) != 16:
            log_error("معرف الجهاز غير صالح")
            return None

        # إزالة الشرطات من الكود وتقسيمه إلى أجزاء
        parts = activation_code.replace('-', ' ').split()
        if len(parts) != 5:
            log_error(f"عدد أجزاء كود التفعيل غير صحيح: {len(parts)}")
            return None

        # استخراج الأجزاء
        hw_prefix = parts[0]
        days_str = parts[1]
        signature = parts[2]
        feature_code = parts[3]
        hw_suffix = parts[4]

        # التحقق من معرف الجهاز
        if hw_prefix != hardware_id[:4] or hw_suffix != hardware_id[-4:]:
            log_error("كود التفعيل غير متوافق مع معرف الجهاز")
            return None

        # إعادة بناء الكود الأصلي للتحقق من التوقيع
        raw_code = hw_prefix + days_str + feature_code + hw_suffix
        expected_signature = hmac.new(SECRET_KEY.encode(), raw_code.encode(), hashlib.sha256).hexdigest()[:4].upper()

        if signature != expected_signature:
            log_error("توقيع كود التفعيل غير صالح")
            return None

        # حساب تاريخ انتهاء الصلاحية
        try:
            days_diff = int(days_str, 16)  # تحويل من سداسي عشري إلى عدد صحيح
            base_date = datetime(2020, 1, 1)
            expiry_date = base_date + timedelta(days=days_diff)

            if datetime.now() > expiry_date:
                log_error("كود التفعيل منتهي الصلاحية")
                return None
        except ValueError:
            log_error("تنسيق تاريخ انتهاء الصلاحية غير صالح")
            return None

        # استخراج الميزات المفعلة
        features = []
        if 'B' in feature_code:
            features.append("basic")
        if 'A' in feature_code:
            features.append("advanced")
        if 'R' in feature_code:
            features.append("reports")
        if 'K' in feature_code:
            features.append("backup")

        # إرجاع معلومات التفعيل
        return {
            'hardware_id': hardware_id,
            'expiry_date': expiry_date,
            'features': features,
            'days_left': (expiry_date - datetime.now()).days
        }
    except Exception as e:
        log_error(f"خطأ في التحقق من كود التفعيل: {str(e)}")
        return None

def is_activated():
    """
    التحقق مما إذا كان البرنامج مفعلًا

    Returns:
        bool: True إذا كان البرنامج مفعلًا، False خلاف ذلك
    """
    # تجاوز التفعيل للتطوير
    return True

    try:
        from database import Activation
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker

        # إنشاء اتصال بقاعدة البيانات
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        # البحث عن معلومات التفعيل
        activation = session.query(Activation).first()
        session.close()

        if not activation:
            return False

        # التحقق من كود التفعيل
        hardware_id = get_hardware_id()
        activation_info = validate_activation_code(activation.activation_code, hardware_id)

        if not activation_info:
            return False

        # التحقق من تاريخ انتهاء الصلاحية
        if activation_info['days_left'] <= 0:
            return False

        return True
    except Exception as e:
        log_error(f"خطأ في التحقق من تفعيل البرنامج: {str(e)}")
        return False

def get_activation_info():
    """
    الحصول على معلومات التفعيل

    Returns:
        dict: معلومات التفعيل إذا كان البرنامج مفعلًا، None خلاف ذلك
    """
    try:
        from database import Activation
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker

        # إنشاء اتصال بقاعدة البيانات
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        # البحث عن معلومات التفعيل
        activation = session.query(Activation).first()
        session.close()

        if not activation:
            return None

        # التحقق من كود التفعيل
        hardware_id = get_hardware_id()
        activation_info = validate_activation_code(activation.activation_code, hardware_id)

        return activation_info
    except Exception as e:
        log_error(f"خطأ في الحصول على معلومات التفعيل: {str(e)}")
        return None

def save_activation_code(activation_code):
    """
    حفظ كود التفعيل في قاعدة البيانات

    Args:
        activation_code (str): كود التفعيل

    Returns:
        bool: True إذا تم الحفظ بنجاح، False خلاف ذلك
    """
    try:
        from database import Activation
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker

        # التحقق من صحة كود التفعيل
        hardware_id = get_hardware_id()
        activation_info = validate_activation_code(activation_code, hardware_id)

        if not activation_info:
            return False

        # إنشاء اتصال بقاعدة البيانات
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        # البحث عن معلومات التفعيل
        activation = session.query(Activation).first()

        # تحويل قائمة الميزات إلى سلسلة نصية
        features_str = ",".join(activation_info['features']) if activation_info.get('features') else ""

        if activation:
            # تحديث كود التفعيل
            activation.activation_code = activation_code
            activation.activation_date = datetime.now()
            activation.hardware_id = hardware_id
            activation.expiry_date = activation_info['expiry_date']
            activation.features = features_str
        else:
            # إنشاء سجل تفعيل جديد
            activation = Activation(
                activation_code=activation_code,
                activation_date=datetime.now(),
                hardware_id=hardware_id,
                expiry_date=activation_info['expiry_date'],
                features=features_str,
                default_expiry_days=365,  # Valor predeterminado
                notify_before_days=30     # Valor predeterminado
            )
            session.add(activation)

        # حفظ التغييرات
        session.commit()
        session.close()

        return True
    except Exception as e:
        log_error(f"خطأ في حفظ كود التفعيل: {str(e)}")
        return False
