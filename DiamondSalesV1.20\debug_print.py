#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from PyQt6.QtWidgets import QApplication
from cash_box_screen import شاشة_صندوق_النقدية
from db_session import session_scope

class TestUser:
    def __init__(self):
        self.id = 1
        self.username = "admin"
        self.role = "admin"

def debug_print_function():
    app = QApplication(sys.argv)
    
    # إنشاء مستخدم وهمي
    user = TestUser()
    
    # إنشاء شاشة صندوق النقدية
    cash_screen = شاشة_صندوق_النقدية(user)
    
    # تحميل البيانات أولاً
    cash_screen.load_data()
    
    print(f"عدد الصفوف في الجدول: {cash_screen.transactions_table.rowCount()}")
    print(f"عدد الأعمدة في الجدول: {cash_screen.transactions_table.columnCount()}")
    
    # التحقق من وجود العناصر
    print(f"total_balance_label موجود: {hasattr(cash_screen, 'total_balance_label')}")
    print(f"total_in_label موجود: {hasattr(cash_screen, 'total_in_label')}")
    print(f"total_out_label موجود: {hasattr(cash_screen, 'total_out_label')}")
    
    if hasattr(cash_screen, 'total_balance_label'):
        print(f"نص الرصيد: '{cash_screen.total_balance_label.text()}'")
    if hasattr(cash_screen, 'total_in_label'):
        print(f"نص الإيداعات: '{cash_screen.total_in_label.text()}'")
    if hasattr(cash_screen, 'total_out_label'):
        print(f"نص المسحوبات: '{cash_screen.total_out_label.text()}'")
    
    # اختبار دالة إنشاء HTML مع معالجة الأخطاء
    try:
        print("بدء إنشاء محتوى HTML...")
        html_content = cash_screen.generate_cash_report_html()
        print(f"تم إنشاء محتوى HTML. الطول: {len(html_content)} حرف")
        
        if html_content:
            # حفظ المحتوى في ملف للاختبار
            test_file = "debug_cash_report.html"
            with open(test_file, 'w', encoding='utf-8') as file:
                file.write(html_content)
            print(f"تم حفظ الملف: {test_file}")
        else:
            print("محتوى HTML فارغ!")
        
    except Exception as e:
        print(f"خطأ في إنشاء HTML: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_print_function()
