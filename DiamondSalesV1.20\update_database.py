import sqlite3
import os
from datetime import datetime

def update_database_structure():
    print("جاري تحديث هيكل قاعدة البيانات...")

    # التأكد من وجود قاعدة البيانات
    if not os.path.exists("diamond_sales.db"):
        print("خطأ: لم يتم العثور على قاعدة البيانات")
        return False

    # الاتصال بقاعدة البيانات
    try:
        conn = sqlite3.connect("diamond_sales.db")
        cursor = conn.cursor()

        # التحقق من وجود الأعمدة في جدول الإعدادات
        cursor.execute("PRAGMA table_info(settings)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        # إضافة العمود backup_path إذا لم يكن موجوداً
        if "backup_path" not in column_names:
            cursor.execute("ALTER TABLE settings ADD COLUMN backup_path TEXT")
            print("تم إضافة عمود backup_path")

        # إضافة العمود last_backup_date إذا لم يكن موجوداً
        if "last_backup_date" not in column_names:
            cursor.execute("ALTER TABLE settings ADD COLUMN last_backup_date TIMESTAMP")
            print("تم إضافة عمود last_backup_date")

        # إضافة العمود auto_backup إذا لم يكن موجوداً
        if "auto_backup" not in column_names:
            cursor.execute("ALTER TABLE settings ADD COLUMN auto_backup BOOLEAN DEFAULT 1")
            print("تم إضافة عمود auto_backup")

        # التحقق من وجود الأعمدة في جدول data_locks
        cursor.execute("PRAGMA table_info(data_locks)")
        lock_columns = cursor.fetchall()
        lock_column_names = [col[1] for col in lock_columns]

        # إضافة العمود locked_by_user_id إذا لم يكن موجوداً
        if "locked_by_user_id" not in lock_column_names:
            cursor.execute("ALTER TABLE data_locks ADD COLUMN locked_by_user_id INTEGER REFERENCES users(id)")
            print("تم إضافة عمود locked_by_user_id")

        # التحقق من وجود عمود notes في جدول المبيعات
        cursor.execute("PRAGMA table_info(sales)")
        sales_columns = cursor.fetchall()
        sales_column_names = [col[1] for col in sales_columns]

        # إضافة العمود notes إذا لم يكن موجوداً
        if "notes" not in sales_column_names:
            cursor.execute("ALTER TABLE sales ADD COLUMN notes TEXT")
            print("تم إضافة عمود notes إلى جدول المبيعات")

        # التحقق من وجود أعمدة الخط في جدول الإعدادات
        if "font_family" not in column_names:
            cursor.execute("ALTER TABLE settings ADD COLUMN font_family TEXT DEFAULT 'Arial'")
            print("تم إضافة عمود font_family إلى جدول الإعدادات")

        if "font_size" not in column_names:
            cursor.execute("ALTER TABLE settings ADD COLUMN font_size INTEGER DEFAULT 10")
            print("تم إضافة عمود font_size إلى جدول الإعدادات")

        # التحقق من وجود عمود receipt_id في جدول cash_transactions
        cursor.execute("PRAGMA table_info(cash_transactions)")
        cash_trans_columns = cursor.fetchall()
        cash_trans_column_names = [col[1] for col in cash_trans_columns]

        # إضافة العمود receipt_id إذا لم يكن موجوداً
        if "receipt_id" not in cash_trans_column_names:
            cursor.execute("ALTER TABLE cash_transactions ADD COLUMN receipt_id INTEGER")
            print("تم إضافة عمود receipt_id إلى جدول cash_transactions")

        conn.commit()
        print("تم تحديث هيكل قاعدة البيانات بنجاح")
        return True
    except Exception as e:
        print(f"حدث خطأ أثناء تحديث قاعدة البيانات: {str(e)}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    update_database_structure()