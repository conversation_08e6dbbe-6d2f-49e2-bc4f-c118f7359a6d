(['F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\main.py'],
 ['F:\\DiamondSalesV1.30\\DiamondSalesV1.20'],
 ['sqlalchemy.sql.default_comparator',
  'sqlalchemy.ext.baked',
  'PyQt6.QtSvg',
  'PyQt6.QtPrintSupport',
  'bcrypt'],
 [('F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('assets\\add_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\add_icon.ico',
   'DATA'),
  ('assets\\add_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\add_icon.svg',
   'DATA'),
  ('assets\\cancel_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\cancel_icon.ico',
   'DATA'),
  ('assets\\cancel_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\cancel_icon.svg',
   'DATA'),
  ('assets\\customers_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\customers_icon.ico',
   'DATA'),
  ('assets\\customers_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\customers_icon.svg',
   'DATA'),
  ('assets\\delete_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\delete_icon.ico',
   'DATA'),
  ('assets\\delete_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\delete_icon.svg',
   'DATA'),
  ('assets\\diamond_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\diamond_icon.ico',
   'DATA'),
  ('assets\\diamond_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\diamond_icon.svg',
   'DATA'),
  ('assets\\edit_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\edit_icon.ico',
   'DATA'),
  ('assets\\edit_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\edit_icon.svg',
   'DATA'),
  ('assets\\export_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\export_icon.ico',
   'DATA'),
  ('assets\\export_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\export_icon.svg',
   'DATA'),
  ('assets\\eye_closed.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_closed.ico',
   'DATA'),
  ('assets\\eye_closed.png',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_closed.png',
   'DATA'),
  ('assets\\eye_closed.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_closed.svg',
   'DATA'),
  ('assets\\eye_open.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_open.ico',
   'DATA'),
  ('assets\\eye_open.png',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_open.png',
   'DATA'),
  ('assets\\eye_open.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_open.svg',
   'DATA'),
  ('assets\\import_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\import_icon.ico',
   'DATA'),
  ('assets\\import_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\import_icon.svg',
   'DATA'),
  ('assets\\inventory_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\inventory_icon.ico',
   'DATA'),
  ('assets\\inventory_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\inventory_icon.svg',
   'DATA'),
  ('assets\\login_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\login_icon.ico',
   'DATA'),
  ('assets\\login_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\login_icon.svg',
   'DATA'),
  ('assets\\logout_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\logout_icon.ico',
   'DATA'),
  ('assets\\logout_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\logout_icon.svg',
   'DATA'),
  ('assets\\print_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\print_icon.ico',
   'DATA'),
  ('assets\\print_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\print_icon.svg',
   'DATA'),
  ('assets\\purchases_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\purchases_icon.ico',
   'DATA'),
  ('assets\\purchases_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\purchases_icon.svg',
   'DATA'),
  ('assets\\reports_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\reports_icon.ico',
   'DATA'),
  ('assets\\reports_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\reports_icon.svg',
   'DATA'),
  ('assets\\sales_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\sales_icon.ico',
   'DATA'),
  ('assets\\sales_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\sales_icon.svg',
   'DATA'),
  ('assets\\save_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\save_icon.ico',
   'DATA'),
  ('assets\\save_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\save_icon.svg',
   'DATA'),
  ('assets\\search_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\search_icon.ico',
   'DATA'),
  ('assets\\search_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\search_icon.svg',
   'DATA'),
  ('assets\\settings_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\settings_icon.ico',
   'DATA'),
  ('assets\\settings_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\settings_icon.svg',
   'DATA'),
  ('assets\\suppliers_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\suppliers_icon.ico',
   'DATA'),
  ('assets\\suppliers_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\suppliers_icon.svg',
   'DATA'),
  ('assets\\users_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\users_icon.ico',
   'DATA'),
  ('assets\\users_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\users_icon.svg',
   'DATA'),
  ('assets\\vouchers_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\vouchers_icon.ico',
   'DATA'),
  ('assets\\vouchers_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\vouchers_icon.svg',
   'DATA'),
  ('diamond_sales.db',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\diamond_sales.db',
   'DATA'),
  ('translations\\__init__.py',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__init__.py',
   'DATA'),
  ('translations\\__pycache__\\__init__.cpython-311.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('translations\\__pycache__\\__init__.cpython-313.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('translations\\__pycache__\\ar.cpython-311.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\ar.cpython-311.pyc',
   'DATA'),
  ('translations\\__pycache__\\ar.cpython-313.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\ar.cpython-313.pyc',
   'DATA'),
  ('translations\\__pycache__\\en.cpython-313.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\en.cpython-313.pyc',
   'DATA'),
  ('translations\\ar.py',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\ar.py',
   'DATA'),
  ('translations\\en.py',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\en.py',
   'DATA')],
 '3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('main', 'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\main.py', 'PYSOURCE')],
 [('pkg_resources',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.tags',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\subprocess.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\selectors.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextlib.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\signal.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\struct.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\copy.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\string.py',
   'PYMODULE'),
  ('packaging.metadata',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getopt.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gettext.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\quopri.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socket.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\random.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\argparse.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bz2.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hashlib.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bisect.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\calendar.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_strptime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ast.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('packaging._elffile',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\csv.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\token.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('typing_extensions',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ssl.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\queue.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\runpy.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\secrets.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\netrc.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\configparser.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\difflib.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.util',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\py_compile.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site.py',
   'PYMODULE'),
  ('_pyrepl.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\main.py',
   'PYMODULE'),
  ('_pyrepl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\curses.py',
   'PYMODULE'),
  ('curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl.input',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\keymap.py',
   'PYMODULE'),
  ('_pyrepl.types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\types.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\commands.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tty.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\reader.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\utils.py',
   'PYMODULE'),
  ('_colorize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_colorize.py',
   'PYMODULE'),
  ('_pyrepl.console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\console.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\codeop.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\trace.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\readline.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('_ios_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shlex.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.glob',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\glob.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\opcode.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('setuptools._imp',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_distutils_hack',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.markers',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\typing.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipimport.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\textwrap.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tempfile.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\plistlib.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\platform.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\inspect.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\__future__.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pathlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('bcrypt',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('PyQt6',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\row.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py',
   'PYMODULE'),
  ('greenlet',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\uuid.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\create.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\context.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\future\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\future\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects._typing',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.operators',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.asyncio',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\asyncio.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadb',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.aioodbc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.aioodbc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('ui_utils',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\ui_utils.py',
   'PYMODULE'),
  ('activation_screen',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\activation_screen.py',
   'PYMODULE'),
  ('activation',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\activation.py',
   'PYMODULE'),
  ('app_state',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\app_state.py',
   'PYMODULE'),
  ('logger', 'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\logger.py', 'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\smtplib.py',
   'PYMODULE'),
  ('db_session',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\db_session.py',
   'PYMODULE'),
  ('translations',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__init__.py',
   'PYMODULE'),
  ('database',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\database.py',
   'PYMODULE'),
  ('sqlalchemy.ext.declarative',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\declarative\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.declarative.extensions',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\declarative\\extensions.py',
   'PYMODULE'),
  ('login_screen',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\login_screen.py',
   'PYMODULE'),
  ('reset_password',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\reset_password.py',
   'PYMODULE'),
  ('email_utils',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\email_utils.py',
   'PYMODULE'),
  ('email.mime.multipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\__init__.py',
   'PYMODULE'),
  ('email.mime.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\base.py',
   'PYMODULE'),
  ('email.mime.text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\text.py',
   'PYMODULE'),
  ('email.mime.nonmultipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('security',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\security.py',
   'PYMODULE'),
  ('dashboard',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\dashboard.py',
   'PYMODULE'),
  ('reports_screen',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\reports_screen.py',
   'PYMODULE'),
  ('xlsxwriter',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\__init__.py',
   'PYMODULE'),
  ('xlsxwriter.workbook',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\workbook.py',
   'PYMODULE'),
  ('xlsxwriter.utility',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\utility.py',
   'PYMODULE'),
  ('xlsxwriter.sharedstrings',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\sharedstrings.py',
   'PYMODULE'),
  ('xlsxwriter.packager',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\packager.py',
   'PYMODULE'),
  ('xlsxwriter.vml',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\vml.py',
   'PYMODULE'),
  ('xlsxwriter.theme',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\theme.py',
   'PYMODULE'),
  ('xlsxwriter.table',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\table.py',
   'PYMODULE'),
  ('xlsxwriter.styles',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\styles.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_types',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\rich_value_types.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_structure',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\rich_value_structure.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_rel',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\rich_value_rel.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\rich_value.py',
   'PYMODULE'),
  ('xlsxwriter.relationships',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\relationships.py',
   'PYMODULE'),
  ('xlsxwriter.metadata',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\metadata.py',
   'PYMODULE'),
  ('xlsxwriter.feature_property_bag',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\feature_property_bag.py',
   'PYMODULE'),
  ('xlsxwriter.custom',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\custom.py',
   'PYMODULE'),
  ('xlsxwriter.core',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\core.py',
   'PYMODULE'),
  ('xlsxwriter.contenttypes',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\contenttypes.py',
   'PYMODULE'),
  ('xlsxwriter.comments',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\comments.py',
   'PYMODULE'),
  ('xlsxwriter.app',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\app.py',
   'PYMODULE'),
  ('xlsxwriter.format',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\format.py',
   'PYMODULE'),
  ('xlsxwriter.exceptions',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\exceptions.py',
   'PYMODULE'),
  ('xlsxwriter.chartsheet',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\chartsheet.py',
   'PYMODULE'),
  ('xlsxwriter.drawing',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\drawing.py',
   'PYMODULE'),
  ('xlsxwriter.shape',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\shape.py',
   'PYMODULE'),
  ('xlsxwriter.url',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\url.py',
   'PYMODULE'),
  ('xlsxwriter.chart_stock',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_stock.py',
   'PYMODULE'),
  ('xlsxwriter.chart_scatter',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_scatter.py',
   'PYMODULE'),
  ('xlsxwriter.chart_radar',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_radar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_line',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_line.py',
   'PYMODULE'),
  ('xlsxwriter.chart_doughnut',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_doughnut.py',
   'PYMODULE'),
  ('xlsxwriter.chart_column',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_column.py',
   'PYMODULE'),
  ('xlsxwriter.chart_bar',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_bar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_area',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_area.py',
   'PYMODULE'),
  ('xlsxwriter.image',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\image.py',
   'PYMODULE'),
  ('xlsxwriter.worksheet',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\worksheet.py',
   'PYMODULE'),
  ('xlsxwriter.chart_pie',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_pie.py',
   'PYMODULE'),
  ('xlsxwriter.chart',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\chart.py',
   'PYMODULE'),
  ('xlsxwriter.xmlwriter',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\xlsxwriter\\xmlwriter.py',
   'PYMODULE'),
  ('vouchers_screen',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\vouchers_screen.py',
   'PYMODULE'),
  ('suppliers_screen',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\suppliers_screen.py',
   'PYMODULE'),
  ('customers_screen',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\customers_screen.py',
   'PYMODULE'),
  ('purchases_screen',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\purchases_screen.py',
   'PYMODULE'),
  ('sales_screen',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\sales_screen.py',
   'PYMODULE'),
  ('categories_units_screen',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\categories_units_screen.py',
   'PYMODULE'),
  ('users_screen',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\users_screen.py',
   'PYMODULE'),
  ('settings_screen',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\settings_screen.py',
   'PYMODULE'),
  ('permissions',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\permissions.py',
   'PYMODULE')],
 [('python313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('PyQt6\\QtPrintSupport.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\sip.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtSvg.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\QtSvg.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\collections.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\util.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\processors.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\greenlet\\_greenlet.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python3.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6PrintSupport.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6PrintSupport.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin\\ucrtbase.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('assets\\add_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\add_icon.ico',
   'DATA'),
  ('assets\\add_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\add_icon.svg',
   'DATA'),
  ('assets\\cancel_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\cancel_icon.ico',
   'DATA'),
  ('assets\\cancel_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\cancel_icon.svg',
   'DATA'),
  ('assets\\customers_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\customers_icon.ico',
   'DATA'),
  ('assets\\customers_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\customers_icon.svg',
   'DATA'),
  ('assets\\delete_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\delete_icon.ico',
   'DATA'),
  ('assets\\delete_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\delete_icon.svg',
   'DATA'),
  ('assets\\diamond_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\diamond_icon.ico',
   'DATA'),
  ('assets\\diamond_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\diamond_icon.svg',
   'DATA'),
  ('assets\\edit_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\edit_icon.ico',
   'DATA'),
  ('assets\\edit_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\edit_icon.svg',
   'DATA'),
  ('assets\\export_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\export_icon.ico',
   'DATA'),
  ('assets\\export_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\export_icon.svg',
   'DATA'),
  ('assets\\eye_closed.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_closed.ico',
   'DATA'),
  ('assets\\eye_closed.png',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_closed.png',
   'DATA'),
  ('assets\\eye_closed.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_closed.svg',
   'DATA'),
  ('assets\\eye_open.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_open.ico',
   'DATA'),
  ('assets\\eye_open.png',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_open.png',
   'DATA'),
  ('assets\\eye_open.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_open.svg',
   'DATA'),
  ('assets\\import_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\import_icon.ico',
   'DATA'),
  ('assets\\import_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\import_icon.svg',
   'DATA'),
  ('assets\\inventory_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\inventory_icon.ico',
   'DATA'),
  ('assets\\inventory_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\inventory_icon.svg',
   'DATA'),
  ('assets\\login_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\login_icon.ico',
   'DATA'),
  ('assets\\login_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\login_icon.svg',
   'DATA'),
  ('assets\\logout_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\logout_icon.ico',
   'DATA'),
  ('assets\\logout_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\logout_icon.svg',
   'DATA'),
  ('assets\\print_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\print_icon.ico',
   'DATA'),
  ('assets\\print_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\print_icon.svg',
   'DATA'),
  ('assets\\purchases_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\purchases_icon.ico',
   'DATA'),
  ('assets\\purchases_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\purchases_icon.svg',
   'DATA'),
  ('assets\\reports_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\reports_icon.ico',
   'DATA'),
  ('assets\\reports_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\reports_icon.svg',
   'DATA'),
  ('assets\\sales_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\sales_icon.ico',
   'DATA'),
  ('assets\\sales_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\sales_icon.svg',
   'DATA'),
  ('assets\\save_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\save_icon.ico',
   'DATA'),
  ('assets\\save_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\save_icon.svg',
   'DATA'),
  ('assets\\search_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\search_icon.ico',
   'DATA'),
  ('assets\\search_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\search_icon.svg',
   'DATA'),
  ('assets\\settings_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\settings_icon.ico',
   'DATA'),
  ('assets\\settings_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\settings_icon.svg',
   'DATA'),
  ('assets\\suppliers_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\suppliers_icon.ico',
   'DATA'),
  ('assets\\suppliers_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\suppliers_icon.svg',
   'DATA'),
  ('assets\\users_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\users_icon.ico',
   'DATA'),
  ('assets\\users_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\users_icon.svg',
   'DATA'),
  ('assets\\vouchers_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\vouchers_icon.ico',
   'DATA'),
  ('assets\\vouchers_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\vouchers_icon.svg',
   'DATA'),
  ('diamond_sales.db',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\diamond_sales.db',
   'DATA'),
  ('translations\\__init__.py',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__init__.py',
   'DATA'),
  ('translations\\__pycache__\\__init__.cpython-311.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('translations\\__pycache__\\__init__.cpython-313.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('translations\\__pycache__\\ar.cpython-311.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\ar.cpython-311.pyc',
   'DATA'),
  ('translations\\__pycache__\\ar.cpython-313.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\ar.cpython-313.pyc',
   'DATA'),
  ('translations\\__pycache__\\en.cpython-313.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\en.cpython-313.pyc',
   'DATA'),
  ('translations\\ar.py',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\ar.py',
   'DATA'),
  ('translations\\en.py',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\en.py',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ka.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('base_library.zip',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\diamond_sales\\base_library.zip',
   'DATA')])
