#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
from PyQt6.QtWidgets import QApplication
from cash_box_screen import شاشة_صندوق_النقدية
from database import User

# إنشاء مستخدم وهمي للاختبار
class TestUser:
    def __init__(self):
        self.id = 1
        self.username = "admin"
        self.role = "admin"

def main():
    app = QApplication(sys.argv)
    
    # إنشاء مستخدم وهمي
    user = TestUser()
    
    # إنشاء شاشة صندوق النقدية
    cash_screen = شاشة_صندوق_النقدية(user)
    cash_screen.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
