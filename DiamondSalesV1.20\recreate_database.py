#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لإعادة إنشاء قاعدة البيانات
يقوم بحذف قاعدة البيانات القديمة وإنشاء قاعدة بيانات جديدة مع الهيكل المحدث
"""

import os
import sqlite3
import shutil
from datetime import datetime
import sys

def log_message(message):
    """تسجيل رسالة مع الوقت"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{timestamp} - {message}")

def backup_database():
    """عمل نسخة احتياطية من قاعدة البيانات الحالية"""
    db_path = "diamond_sales.db"
    if not os.path.exists(db_path):
        log_message("لا توجد قاعدة بيانات للنسخ الاحتياطي")
        return False

    # إنشاء مجلد للنسخ الاحتياطي إذا لم يكن موجودًا
    backup_dir = "database_backups"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)

    # إنشاء اسم ملف النسخة الاحتياطية مع الوقت
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = os.path.join(backup_dir, f"diamond_sales_backup_{timestamp}.db")

    try:
        # نسخ قاعدة البيانات
        shutil.copy2(db_path, backup_path)
        log_message(f"تم عمل نسخة احتياطية من قاعدة البيانات في: {backup_path}")
        return True
    except Exception as e:
        log_message(f"خطأ أثناء عمل نسخة احتياطية: {str(e)}")
        return False

def delete_database():
    """حذف قاعدة البيانات الحالية"""
    db_path = "diamond_sales.db"
    if not os.path.exists(db_path):
        log_message("قاعدة البيانات غير موجودة")
        return True

    try:
        os.remove(db_path)
        log_message("تم حذف قاعدة البيانات بنجاح")
        return True
    except Exception as e:
        log_message(f"خطأ أثناء حذف قاعدة البيانات: {str(e)}")
        return False

def create_database():
    """إنشاء قاعدة بيانات جديدة مع الهيكل المحدث"""
    try:
        # إنشاء اتصال بقاعدة البيانات الجديدة
        conn = sqlite3.connect("diamond_sales.db")
        cursor = conn.cursor()

        # إنشاء جدول المستخدمين
        cursor.execute("""
        CREATE TABLE users (
            id INTEGER PRIMARY KEY,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT,
            email TEXT,
            phone TEXT,
            role TEXT,
            is_active BOOLEAN DEFAULT 1,
            last_login TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            failed_login_attempts INTEGER DEFAULT 0,
            remember_token TEXT,
            reset_password_token TEXT,
            reset_password_expires TIMESTAMP
        )
        """)

        # إنشاء جدول الإعدادات
        cursor.execute("""
        CREATE TABLE settings (
            id INTEGER PRIMARY KEY,
            company_name TEXT,
            company_address TEXT,
            company_phone TEXT,
            company_email TEXT,
            tax_rate REAL DEFAULT 0.0,
            currency TEXT DEFAULT 'USD',
            language TEXT DEFAULT 'ar',
            theme TEXT DEFAULT 'light',
            backup_path TEXT,
            last_backup_date TIMESTAMP,
            auto_backup BOOLEAN DEFAULT 1,
            font_family TEXT DEFAULT 'Arial',
            font_size INTEGER DEFAULT 10,
            exchange_rate REAL DEFAULT 3.75,
            default_currency TEXT DEFAULT 'USD'
        )
        """)

        # إنشاء جدول صندوق النقدية
        cursor.execute("""
        CREATE TABLE cash_box (
            id INTEGER PRIMARY KEY,
            balance REAL NOT NULL DEFAULT 0.0,
            last_updated TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        """)

        # إنشاء جدول حركات النقدية
        cursor.execute("""
        CREATE TABLE cash_transactions (
            id INTEGER PRIMARY KEY,
            cash_box_id INTEGER REFERENCES cash_box(id) ON DELETE CASCADE,
            transaction_type TEXT NOT NULL,
            amount REAL NOT NULL,
            balance_after REAL NOT NULL,
            transaction_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            reference TEXT,
            description TEXT,
            created_by INTEGER REFERENCES users(id),
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            receipt_id INTEGER
        )
        """)

        # إنشاء فهارس للبحث السريع
        cursor.execute("CREATE INDEX idx_cash_trans_date ON cash_transactions(transaction_date)")
        cursor.execute("CREATE INDEX idx_cash_trans_type ON cash_transactions(transaction_type)")

        # إنشاء مستخدم افتراضي (admin/admin123)
        # استخدام كلمة مرور بسيطة بدون تشفير للتجربة
        cursor.execute("""
        INSERT INTO users (username, password_hash, full_name, role, is_active)
        VALUES ('admin', 'admin123', 'مدير النظام', 'admin', 1)
        """)

        # إنشاء سجل صندوق النقدية
        cursor.execute("""
        INSERT INTO cash_box (balance, last_updated)
        VALUES (0.0, CURRENT_TIMESTAMP)
        """)

        # حفظ التغييرات
        conn.commit()
        log_message("تم إنشاء قاعدة البيانات بنجاح مع الهيكل المحدث")
        return True
    except Exception as e:
        log_message(f"خطأ أثناء إنشاء قاعدة البيانات: {str(e)}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

def main():
    """الدالة الرئيسية"""
    log_message("بدء عملية إعادة إنشاء قاعدة البيانات...")

    # عمل نسخة احتياطية أولاً
    if backup_database():
        log_message("تم عمل نسخة احتياطية بنجاح")
    else:
        response = input("فشل عمل نسخة احتياطية. هل تريد المتابعة على أي حال؟ (y/n): ")
        if response.lower() != 'y':
            log_message("تم إلغاء العملية")
            return

    # حذف قاعدة البيانات
    if not delete_database():
        log_message("فشل حذف قاعدة البيانات. إلغاء العملية.")
        return

    # إنشاء قاعدة بيانات جديدة
    if create_database():
        log_message("تمت عملية إعادة إنشاء قاعدة البيانات بنجاح")
    else:
        log_message("فشل إنشاء قاعدة البيانات الجديدة")

if __name__ == "__main__":
    # التأكد من أن المستخدم يريد حقًا إعادة إنشاء قاعدة البيانات
    print("تحذير: سيؤدي هذا إلى حذف جميع البيانات الحالية وإنشاء قاعدة بيانات جديدة.")
    print("سيتم عمل نسخة احتياطية قبل الحذف.")
    response = input("هل أنت متأكد أنك تريد المتابعة؟ (y/n): ")

    if response.lower() == 'y':
        main()
    else:
        print("تم إلغاء العملية")
