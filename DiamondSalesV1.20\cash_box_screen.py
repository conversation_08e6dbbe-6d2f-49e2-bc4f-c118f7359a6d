from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QTableWidget, QTableWidgetItem, QDateEdit, QComboBox,
                            QLineEdit, QFormLayout, QMessageBox, QHeaderView, QGroupBox,
                            QDialog, QDoubleSpinBox, QTextEdit)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QColor, QFont
from datetime import datetime
from sqlalchemy import func, desc, or_
from ui_utils import confirm_dialog
from db_session import session_scope
from database import صندوق_النقدية, حركة_نقدية, Sale, Purchase, Receipt, Supplier, Customer
from permissions import check_permission
import re

class شاشة_صندوق_النقدية(QWidget):
    refresh_signal = pyqtSignal()

    def __init__(self, user):
        super().__init__()
        self.user = user
        self.init_ui()
        self.load_data()

    def init_ui(self):
        self.setWindowTitle("صندوق النقدية")
        self.setGeometry(100, 100, 1000, 600)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # مجموعة ملخص الصندوق
        summary_group = QGroupBox("ملخص الصندوق")
        summary_layout = QHBoxLayout()

        # ملصقات رصيد الصندوق
        self.total_balance_label = QLabel("الرصيد الحالي: 0.00 $ | 0.00 ريال")
        self.total_balance_label.setStyleSheet("font-size: 16px; font-weight: bold; color: green;")

        self.total_in_label = QLabel("إجمالي المدين (الإيداعات): 0.00 $ | 0.00 ريال")
        self.total_in_label.setStyleSheet("color: blue;")

        self.total_out_label = QLabel("إجمالي الدائن (المسحوبات): 0.00 $ | 0.00 ريال")
        self.total_out_label.setStyleSheet("color: red;")

        summary_layout.addWidget(self.total_balance_label)
        summary_layout.addWidget(self.total_in_label)
        summary_layout.addWidget(self.total_out_label)
        summary_group.setLayout(summary_layout)

        # إضافة إلى التخطيط الرئيسي
        main_layout.addWidget(summary_group)

        # مجموعة البحث
        search_group = QGroupBox("بحث")
        search_layout = QHBoxLayout()

        # تاريخ البدء
        start_date_label = QLabel("من تاريخ:")
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-1))

        # تاريخ الانتهاء
        end_date_label = QLabel("إلى تاريخ:")
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate.currentDate())

        # نوع الحركة
        transaction_type_label = QLabel("نوع الحركة:")
        self.transaction_type_combo = QComboBox()
        self.transaction_type_combo.addItem("الكل", "all")
        self.transaction_type_combo.addItem("إيداع", "deposit")
        self.transaction_type_combo.addItem("سحب", "withdraw")

        # زر البحث
        self.search_button = QPushButton("بحث")
        self.search_button.clicked.connect(self.search_transactions)

        # إضافة عناصر البحث إلى تخطيط البحث
        search_layout.addWidget(start_date_label)
        search_layout.addWidget(self.start_date_edit)
        search_layout.addWidget(end_date_label)
        search_layout.addWidget(self.end_date_edit)
        search_layout.addWidget(transaction_type_label)
        search_layout.addWidget(self.transaction_type_combo)
        search_layout.addWidget(self.search_button)
        search_group.setLayout(search_layout)

        # إضافة إلى التخطيط الرئيسي
        main_layout.addWidget(search_group)

        # مجموعة أزرار الإجراءات
        actions_group = QGroupBox("الإجراءات")
        actions_layout = QHBoxLayout()
          # زر إيداع نقدي (مدين)
        self.deposit_button = QPushButton("سند قبض (مدين)")
        self.deposit_button.clicked.connect(self.add_deposit)

        # زر سحب نقدي (دائن)
        self.withdraw_button = QPushButton("سند صرف (دائن)")
        self.withdraw_button.clicked.connect(self.add_withdrawal)

        # زر طباعة التقرير
        self.print_button = QPushButton("طباعة التقرير")
        self.print_button.clicked.connect(self.print_report)

        # إضافة أزرار الإجراءات إلى تخطيط الإجراءات
        actions_layout.addWidget(self.deposit_button)
        actions_layout.addWidget(self.withdraw_button)
        actions_layout.addWidget(self.print_button)

        # التحقق من صلاحيات المستخدم للتعديل
        if not check_permission(self.user, "ادارة_صندوق_النقدية") and self.user.role != "admin":
            self.deposit_button.setEnabled(False)
            self.withdraw_button.setEnabled(False)

        actions_group.setLayout(actions_layout)

        # إضافة إلى التخطيط الرئيسي
        main_layout.addWidget(actions_group)

        # جدول حركات الصندوق
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(9)
        self.transactions_table.setHorizontalHeaderLabels([
            "رقم", "التاريخ", "النوع",
            "مدين ($)", "دائن ($)",
            "مدين (ريال)", "دائن (ريال)",
            "الرصيد التراكمي", "البيان"
        ])
        self.transactions_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.transactions_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        # إضافة إلى التخطيط الرئيسي
        main_layout.addWidget(self.transactions_table)

        # تعيين التخطيط الرئيسي
        self.setLayout(main_layout)

        # الاتصال بإشارة التحديث
        self.refresh_signal.connect(self.load_data)

    def load_data(self):
        """تحميل بيانات الصندوق وحركاته"""
        try:
            with session_scope() as session:
                # استعلام عن رصيد الصندوق
                cash_box = session.query(صندوق_النقدية).first()

                if not cash_box:
                    # إنشاء صندوق جديد إذا لم يكن موجوداً
                    cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                    session.add(cash_box)
                    session.commit()

                # معامل التحويل من دولار إلى ريال
                usd_to_sar_rate = 3.75

                # تحديث ملصقات الملخص
                balance_sar = cash_box.balance * usd_to_sar_rate
                self.total_balance_label.setText(f"الرصيد الحالي: {cash_box.balance:.2f} $ | {balance_sar:.2f} ريال")

                # حساب إجمالي الإيداعات والمسحوبات
                start_date = datetime.combine(self.start_date_edit.date().toPyDate(), datetime.min.time())
                end_date = datetime.combine(self.end_date_edit.date().toPyDate(), datetime.max.time())

                total_deposits = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_date >= start_date,
                    حركة_نقدية.transaction_date <= end_date,
                    حركة_نقدية.transaction_type == "deposit"
                ).scalar() or 0

                total_withdrawals = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_date >= start_date,
                    حركة_نقدية.transaction_date <= end_date,
                    حركة_نقدية.transaction_type == "withdraw"
                ).scalar() or 0

                # حساب القيم بالريال السعودي
                total_deposits_sar = total_deposits * usd_to_sar_rate
                total_withdrawals_sar = total_withdrawals * usd_to_sar_rate

                self.total_in_label.setText(f"إجمالي المدين (الإيداعات): {total_deposits:.2f} $ | {total_deposits_sar:.2f} ريال")
                self.total_out_label.setText(f"إجمالي الدائن (المسحوبات): {total_withdrawals:.2f} $ | {total_withdrawals_sar:.2f} ريال")

                # تحميل الحركات في الجدول
                self.load_transactions(session, start_date, end_date)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الصندوق: {str(e)}")

    def load_transactions(self, session, start_date, end_date, transaction_type="all"):
        """تحميل حركات الصندوق إلى الجدول"""
        try:
            # بناء الاستعلام
            query = session.query(حركة_نقدية).filter(
                حركة_نقدية.transaction_date >= start_date,
                حركة_نقدية.transaction_date <= end_date
            )

            # التأكد من أن قيمة transaction_type صالحة
            if transaction_type and transaction_type != "all":
                query = query.filter(حركة_نقدية.transaction_type == transaction_type)

            # التأكد من وجود صندوق النقدية
            cash_box = session.query(صندوق_النقدية).first()
            if cash_box:
                query = query.filter(حركة_نقدية.cash_box_id == cash_box.id)

            # ترتيب حسب التاريخ تنازلياً
            transactions = query.order_by(desc(حركة_نقدية.transaction_date)).all()

            # تعيين عدد الصفوف
            self.transactions_table.setRowCount(len(transactions))

            # ملء بيانات الجدول
            for row, transaction in enumerate(transactions):
                # رقم العملية
                id_item = QTableWidgetItem(str(transaction.id))
                id_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 0, id_item)

                # التاريخ
                date_item = QTableWidgetItem(transaction.transaction_date.strftime("%Y-%m-%d %H:%M"))
                date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 1, date_item)

                # النوع
                type_text = "إيداع" if transaction.transaction_type == "deposit" else "سحب"
                type_item = QTableWidgetItem(type_text)
                type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

                # تلوين الخلية حسب النوع
                if transaction.transaction_type == "deposit":
                    type_item.setBackground(QColor(200, 250, 200))  # أخضر فاتح للإيداعات
                else:
                    type_item.setBackground(QColor(250, 200, 200))  # أحمر فاتح للمسحوبات

                self.transactions_table.setItem(row, 2, type_item)

                # معامل التحويل من دولار إلى ريال
                usd_to_sar_rate = 3.75

                # تحديد قيم مدين ودائن بالدولار
                debit_usd = transaction.amount if transaction.transaction_type == "deposit" else 0
                credit_usd = transaction.amount if transaction.transaction_type == "withdraw" else 0

                # تحديد قيم مدين ودائن بالريال
                debit_sar = debit_usd * usd_to_sar_rate
                credit_sar = credit_usd * usd_to_sar_rate

                # مدين بالدولار
                debit_usd_item = QTableWidgetItem(f"{debit_usd:.2f}" if debit_usd > 0 else "")
                debit_usd_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 3, debit_usd_item)

                # دائن بالدولار
                credit_usd_item = QTableWidgetItem(f"{credit_usd:.2f}" if credit_usd > 0 else "")
                credit_usd_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 4, credit_usd_item)

                # مدين بالريال
                debit_sar_item = QTableWidgetItem(f"{debit_sar:.2f}" if debit_sar > 0 else "")
                debit_sar_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 5, debit_sar_item)

                # دائن بالريال
                credit_sar_item = QTableWidgetItem(f"{credit_sar:.2f}" if credit_sar > 0 else "")
                credit_sar_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 6, credit_sar_item)

                # الرصيد التراكمي
                balance_item = QTableWidgetItem(f"{transaction.balance_after:.2f} $")
                balance_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 7, balance_item)

                # البيان (الوصف)
                description = transaction.description or ""
                reference = transaction.reference or ""
                full_description = f"{reference} - {description}" if reference and description else (reference or description)
                description_item = QTableWidgetItem(full_description)
                self.transactions_table.setItem(row, 8, description_item)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل حركات الصندوق: {str(e)}")

    def search_transactions(self):
        """البحث في حركات الصندوق حسب المعايير المحددة"""
        try:
            start_date = datetime.combine(self.start_date_edit.date().toPyDate(), datetime.min.time())
            end_date = datetime.combine(self.end_date_edit.date().toPyDate(), datetime.max.time())
            transaction_type = self.transaction_type_combo.currentData()

            with session_scope() as session:
                self.load_transactions(session, start_date, end_date, transaction_type)

                # معامل التحويل من دولار إلى ريال
                usd_to_sar_rate = 3.75

                # تحديث ملخص الصندوق
                cash_box = session.query(صندوق_النقدية).first()
                balance_sar = cash_box.balance * usd_to_sar_rate
                self.total_balance_label.setText(f"الرصيد الحالي: {cash_box.balance:.2f} $ | {balance_sar:.2f} ريال")

                # حساب إجمالي الإيداعات والمسحوبات للفترة المحددة
                total_deposits = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_date >= start_date,
                    حركة_نقدية.transaction_date <= end_date,
                    حركة_نقدية.transaction_type == "deposit"
                ).scalar() or 0

                total_withdrawals = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_date >= start_date,
                    حركة_نقدية.transaction_date <= end_date,
                    حركة_نقدية.transaction_type == "withdraw"
                ).scalar() or 0

                # حساب القيم بالريال السعودي
                total_deposits_sar = total_deposits * usd_to_sar_rate
                total_withdrawals_sar = total_withdrawals * usd_to_sar_rate

                self.total_in_label.setText(f"إجمالي المدين (الإيداعات): {total_deposits:.2f} $ | {total_deposits_sar:.2f} ريال")
                self.total_out_label.setText(f"إجمالي الدائن (المسحوبات): {total_withdrawals:.2f} $ | {total_withdrawals_sar:.2f} ريال")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    def add_deposit(self):
        """إضافة إيداع نقدي جديد"""
        if not check_permission(self.user, "ادارة_صندوق_النقدية") and self.user.role != "admin":
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية لإضافة إيداعات نقدية")
            return

        dialog = حوار_حركة_نقدية("deposit")
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                amount = dialog.amount_spinbox.value()
                description = dialog.description_text.toPlainText()
                reference = dialog.reference_edit.text()
                transaction_date = dialog.date_edit.date().toPyDate()

                with session_scope() as session:
                    # الحصول على الصندوق
                    cash_box = session.query(صندوق_النقدية).first()
                    if not cash_box:
                        cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                        session.add(cash_box)

                    # تحديث رصيد الصندوق
                    new_balance = cash_box.balance + amount
                    cash_box.balance = new_balance
                    cash_box.last_updated = datetime.now()

                    # إضافة حركة جديدة
                    transaction = حركة_نقدية(
                        transaction_type="deposit",
                        amount=amount,
                        balance_after=new_balance,
                        transaction_date=transaction_date,
                        description=description,
                        reference=reference,
                        created_by=self.user.id
                    )
                    session.add(transaction)
                      # حفظ التغييرات
                    session.commit()

                    # تحديث العرض
                    self.refresh_signal.emit()
                    QMessageBox.information(self, "نجاح", f"تم إضافة إيداع نقدي بقيمة {amount:.2f} $ بنجاح")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة الإيداع: {str(e)}")

    def add_withdrawal(self):
        """إضافة سحب نقدي جديد"""
        if not check_permission(self.user, "ادارة_صندوق_النقدية") and self.user.role != "admin":
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية لإضافة سحوبات نقدية")
            return

        dialog = حوار_حركة_نقدية("withdraw")
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                amount = dialog.amount_spinbox.value()
                description = dialog.description_text.toPlainText()
                reference = dialog.reference_edit.text()
                transaction_date = dialog.date_edit.date().toPyDate()

                with session_scope() as session:
                    # الحصول على الصندوق
                    cash_box = session.query(صندوق_النقدية).first()
                    if not cash_box:
                        cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                        session.add(cash_box)

                    # التحقق من كفاية الرصيد
                    if cash_box.balance < amount:
                        QMessageBox.warning(self, "تنبيه", "الرصيد غير كافٍ لإجراء هذه العملية")
                        return

                    # تحديث رصيد الصندوق
                    new_balance = cash_box.balance - amount
                    cash_box.balance = new_balance
                    cash_box.last_updated = datetime.now()

                    # إضافة حركة جديدة
                    transaction = حركة_نقدية(
                        transaction_type="withdraw",
                        amount=amount,
                        balance_after=new_balance,
                        transaction_date=transaction_date,
                        description=description,
                        reference=reference,
                        created_by=self.user.id
                    )
                    session.add(transaction)

                    # حفظ التغييرات
                    session.commit()

                    # تحديث العرض
                    self.refresh_signal.emit()
                    QMessageBox.information(self, "نجاح", f"تم إضافة سحب نقدي بقيمة {amount:.2f} $ بنجاح")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة السحب: {str(e)}")

    def print_report(self):
        """طباعة تقرير حركات الصندوق وفتحه في المتصفح"""
        if not check_permission(self.user, "عرض_صندوق_النقدية") and not check_permission(self.user, "manage_reports") and self.user.role != "admin":
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية لطباعة التقارير")
            return

        try:
            from PyQt6.QtCore import QUrl
            from PyQt6.QtGui import QDesktopServices
            import os
            import tempfile

            # إنشاء محتوى التقرير
            html_content = self.generate_report_html()

            # إنشاء ملف مؤقت للتقرير
            temp_dir = tempfile.gettempdir()
            file_name = f"تقرير_صندوق_النقدية_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            file_path = os.path.join(temp_dir, file_name)

            # كتابة محتوى HTML إلى الملف
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(html_content)

            # فتح الملف في المتصفح الافتراضي
            QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))

            QMessageBox.information(self, "تم", "تم فتح التقرير في المتصفح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")

    def generate_report_html(self):
        """إنشاء محتوى HTML لتقرير حركات الصندوق"""
        start_date = self.start_date_edit.date().toPyDate()
        end_date = self.end_date_edit.date().toPyDate()

        # معامل التحويل من دولار إلى ريال
        usd_to_sar_rate = 3.75

        # الحصول على إجمالي الإيداعات والمسحوبات
        with session_scope() as session:
            cash_box = session.query(صندوق_النقدية).first()

            total_deposits = session.query(func.sum(حركة_نقدية.amount)).filter(
                حركة_نقدية.transaction_date >= start_date,
                حركة_نقدية.transaction_date <= end_date,
                حركة_نقدية.transaction_type == "deposit"
            ).scalar() or 0

            total_withdrawals = session.query(func.sum(حركة_نقدية.amount)).filter(
                حركة_نقدية.transaction_date >= start_date,
                حركة_نقدية.transaction_date <= end_date,
                حركة_نقدية.transaction_type == "withdraw"
            ).scalar() or 0

            # حساب القيم بالريال السعودي
            total_deposits_sar = total_deposits * usd_to_sar_rate
            total_withdrawals_sar = total_withdrawals * usd_to_sar_rate
            current_balance_sar = cash_box.balance * usd_to_sar_rate

            # بناء نص HTML
            html = f"""
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>تقرير حركات الصندوق</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
                    body {{
                        font-family: 'Tajawal', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        direction: rtl;
                        background-color: #f9f9f9;
                        color: #333;
                    }}
                    .container {{
                        max-width: 1200px;
                        margin: 0 auto;
                        background-color: #fff;
                        padding: 30px;
                        border-radius: 10px;
                        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
                    }}
                    .header {{
                        text-align: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 2px solid #eee;
                    }}
                    .title {{
                        font-size: 28px;
                        font-weight: 700;
                        color: #2c3e50;
                        margin-bottom: 10px;
                    }}
                    .subtitle {{
                        font-size: 20px;
                        font-weight: 500;
                        color: #3498db;
                        margin: 10px 0;
                    }}
                    .date-range {{
                        font-size: 16px;
                        color: #7f8c8d;
                        margin-top: 10px;
                    }}
                    .summary {{
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: space-between;
                        margin: 20px 0;
                        background-color: #f8f9fa;
                        padding: 20px;
                        border-radius: 5px;
                    }}
                    .summary-item {{
                        flex: 1;
                        min-width: 200px;
                        margin: 10px;
                        padding: 15px;
                        background-color: white;
                        border-radius: 5px;
                        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                    }}
                    .summary-label {{
                        font-size: 14px;
                        color: #7f8c8d;
                        margin-bottom: 5px;
                    }}
                    .summary-value {{
                        font-size: 18px;
                        font-weight: 700;
                    }}
                    .summary-value.usd {{
                        color: #3498db;
                    }}
                    .summary-value.sar {{
                        color: #2ecc71;
                    }}
                    table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin: 25px 0;
                        font-size: 15px;
                    }}
                    th, td {{
                        padding: 12px 15px;
                        text-align: right;
                        border: 1px solid #ddd;
                    }}
                    th {{
                        background-color: #3498db;
                        color: white;
                        font-weight: 500;
                    }}
                    tr:hover {{
                        background-color: #f5f5f5;
                    }}
                    .deposit {{
                        background-color: #e8f7f2;
                    }}
                    .withdraw {{
                        background-color: #fdf3f3;
                    }}
                    .footer {{
                        margin-top: 40px;
                        text-align: center;
                        font-size: 14px;
                        color: #95a5a6;
                        padding-top: 20px;
                        border-top: 1px solid #eee;
                    }}
                    @media print {{
                        body {{
                            background-color: white;
                            margin: 0;
                            padding: 0;
                        }}
                        .container {{
                            box-shadow: none;
                            max-width: 100%;
                            padding: 15px;
                        }}
                        table {{
                            page-break-inside: auto;
                        }}
                        tr {{
                            page-break-inside: avoid;
                            page-break-after: auto;
                        }}
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <div class="title">نظام إدارة مبيعات الألماس</div>
                        <div class="subtitle">تقرير حركات الصندوق</div>
                        <div class="date-range">للفترة من {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}</div>
                    </div>

                    <div class="summary">
                        <div class="summary-item">
                            <div class="summary-label">الرصيد الحالي</div>
                            <div class="summary-value usd">{cash_box.balance:.2f} $</div>
                            <div class="summary-value sar">{current_balance_sar:.2f} ريال</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">إجمالي المدين (الإيداعات)</div>
                            <div class="summary-value usd">{total_deposits:.2f} $</div>
                            <div class="summary-value sar">{total_deposits_sar:.2f} ريال</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">إجمالي الدائن (المسحوبات)</div>
                            <div class="summary-value usd">{total_withdrawals:.2f} $</div>
                            <div class="summary-value sar">{total_withdrawals_sar:.2f} ريال</div>
                        </div>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>رقم</th>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>مدين ($)</th>
                                <th>دائن ($)</th>
                                <th>مدين (ريال)</th>
                                <th>دائن (ريال)</th>
                                <th>الرصيد التراكمي ($)</th>
                                <th>البيان</th>
                            </tr>
                        </thead>
                        <tbody>
            """

            # إضافة صفوف الحركات
            transactions = session.query(حركة_نقدية).filter(
                حركة_نقدية.transaction_date >= start_date,
                حركة_نقدية.transaction_date <= end_date
            ).order_by(desc(حركة_نقدية.transaction_date)).all()

            for transaction in transactions:
                row_class = "deposit" if transaction.transaction_type == "deposit" else "withdraw"
                type_text = "إيداع" if transaction.transaction_type == "deposit" else "سحب"

                # معالجة المرجع والوصف للتأكد من عرضهما بشكل صحيح
                reference = transaction.reference or ""
                description = transaction.description or ""

                # تحديد قيم مدين ودائن بالدولار
                debit_usd = transaction.amount if transaction.transaction_type == "deposit" else 0
                credit_usd = transaction.amount if transaction.transaction_type == "withdraw" else 0

                # تحديد قيم مدين ودائن بالريال
                debit_sar = debit_usd * usd_to_sar_rate
                credit_sar = credit_usd * usd_to_sar_rate

                # البيان المجمع
                full_description = f"{reference} - {description}" if reference and description else (reference or description)

                # Preparar los valores formateados
                debit_usd_str = f"{debit_usd:.2f}" if debit_usd > 0 else ""
                credit_usd_str = f"{credit_usd:.2f}" if credit_usd > 0 else ""
                debit_sar_str = f"{debit_sar:.2f}" if debit_sar > 0 else ""
                credit_sar_str = f"{credit_sar:.2f}" if credit_sar > 0 else ""

                html += f"""
                    <tr class="{row_class}">
                        <td>{transaction.id}</td>
                        <td>{transaction.transaction_date.strftime('%Y-%m-%d %H:%M')}</td>
                        <td>{type_text}</td>
                        <td>{debit_usd_str}</td>
                        <td>{credit_usd_str}</td>
                        <td>{debit_sar_str}</td>
                        <td>{credit_sar_str}</td>
                        <td>{transaction.balance_after:.2f} $</td>
                        <td>{full_description}</td>
                    </tr>
                """

            html += f"""
                        </tbody>
                    </table>

                    <div class="footer">
                        <p>تم إنشاء هذا التقرير في {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
                    </div>
                </div>
            </body>
            </html>
            """

            return html


class حوار_حركة_نقدية(QDialog):
    """حوار إضافة حركة نقدية جديدة (إيداع أو سحب)"""

    def __init__(self, transaction_type="deposit"):
        super().__init__()
        self.transaction_type = transaction_type
        self.init_ui()

    def init_ui(self):
        # تعيين عنوان الحوار حسب نوع العملية
        title = "سند قبض جديد (مدين)" if self.transaction_type == "deposit" else "سند صرف جديد (دائن)"
        self.setWindowTitle(title)
        self.setGeometry(200, 200, 400, 300)

        # التخطيط الرئيسي
        layout = QVBoxLayout()

        # نموذج الإدخال
        form_layout = QFormLayout()

        # حقل المبلغ بالدولار
        self.amount_spinbox = QDoubleSpinBox()
        self.amount_spinbox.setRange(0, 1000000)
        self.amount_spinbox.setDecimals(2)
        self.amount_spinbox.setSingleStep(100)
        self.amount_spinbox.setValue(0)
        self.amount_spinbox.setPrefix("$ ")
        form_layout.addRow("المبلغ بالدولار:", self.amount_spinbox)

        # عرض المبلغ بالريال (للعرض فقط)
        self.amount_sar_label = QLabel("0.00 ريال")
        self.amount_sar_label.setStyleSheet("color: green; font-weight: bold;")
        form_layout.addRow("المبلغ بالريال:", self.amount_sar_label)

        # تحديث قيمة الريال عند تغيير قيمة الدولار
        self.amount_spinbox.valueChanged.connect(self.update_sar_amount)

        # حقل التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        form_layout.addRow("التاريخ:", self.date_edit)

        # حقل المرجع
        self.reference_edit = QLineEdit()
        form_layout.addRow("المرجع:", self.reference_edit)

        # حقل الوصف
        self.description_text = QTextEdit()
        self.description_text.setPlaceholderText("أدخل وصفاً للعملية...")
        form_layout.addRow("الوصف:", self.description_text)

        # إضافة نموذج الإدخال إلى التخطيط الرئيسي
        layout.addLayout(form_layout)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        # زر الحفظ
        save_button = QPushButton("حفظ")
        save_button.clicked.connect(self.accept)

        # زر الإلغاء
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)

        # إضافة أزرار الإجراءات إلى التخطيط الرئيسي
        layout.addLayout(buttons_layout)

        # تعيين التخطيط الرئيسي
        self.setLayout(layout)

    def update_sar_amount(self):
        """تحديث قيمة المبلغ بالريال عند تغيير قيمة الدولار"""
        usd_amount = self.amount_spinbox.value()
        sar_amount = usd_amount * 3.75  # معامل التحويل من دولار إلى ريال
        self.amount_sar_label.setText(f"{sar_amount:.2f} ريال")

    def accept(self):
        """التحقق من صحة البيانات قبل قبول الحوار"""
        if self.amount_spinbox.value() <= 0:
            QMessageBox.warning(self, "تنبيه", "يجب إدخال مبلغ أكبر من صفر")
            return

        super().accept()
