2025-05-22 19:55:43 - diamond_sales - INFO - تم فتح اتصال جديد بقاعدة البيانات (ID: 2055720502512)
2025-05-22 19:55:43 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 2055720502512)
2025-05-22 19:55:43 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 2055720502512)
2025-05-22 19:55:43 - diamond_sales - ERROR - خطأ في قاعدة البيانات (session_id: 2055712883776): (sqlite3.OperationalError) no such table: receipts
[SQL: SELECT receipts.id AS receipts_id, receipts.sale_id AS receipts_sale_id, receipts.purchase_id AS receipts_purchase_id, receipts.customer_id AS receipts_customer_id, receipts.supplier_id AS receipts_supplier_id, receipts.receipt_type AS receipts_receipt_type, receipts.amount_usd AS receipts_amount_usd, receipts.amount_sar AS receipts_amount_sar, receipts.issue_date AS receipts_issue_date 
FROM receipts 
WHERE receipts.receipt_type = ?]
[parameters: ('CashOut',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8): (sqlite3.OperationalError) no such table: receipts
[SQL: SELECT receipts.id AS receipts_id, receipts.sale_id AS receipts_sale_id, receipts.purchase_id AS receipts_purchase_id, receipts.customer_id AS receipts_customer_id, receipts.supplier_id AS receipts_supplier_id, receipts.receipt_type AS receipts_receipt_type, receipts.amount_usd AS receipts_amount_usd, receipts.amount_sar AS receipts_amount_sar, receipts.issue_date AS receipts_issue_date 
FROM receipts 
WHERE receipts.receipt_type = ?]
[parameters: ('CashOut',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: receipts

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "f:\DiamondSalesV1.30\DiamondSalesV1.20\db_session.py", line 70, in session_scope
    yield session
  File "f:\DiamondSalesV1.30\DiamondSalesV1.20\test_cash_box_withdrawals.py", line 30, in check_withdrawal_vouchers
    ).all()
      ~~~^^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ~~~~~~~~~~^^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
        params,
        ^^^^^^^
        execution_options={"_sa_orm_load_options": self.load_options},
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: receipts
[SQL: SELECT receipts.id AS receipts_id, receipts.sale_id AS receipts_sale_id, receipts.purchase_id AS receipts_purchase_id, receipts.customer_id AS receipts_customer_id, receipts.supplier_id AS receipts_supplier_id, receipts.receipt_type AS receipts_receipt_type, receipts.amount_usd AS receipts_amount_usd, receipts.amount_sar AS receipts_amount_sar, receipts.issue_date AS receipts_issue_date 
FROM receipts 
WHERE receipts.receipt_type = ?]
[parameters: ('CashOut',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-22 19:56:40 - diamond_sales - INFO - تم فتح اتصال جديد بقاعدة البيانات (ID: 2509111771440)
2025-05-22 19:56:40 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 2509111771440)
2025-05-22 19:56:40 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 2509111771440)
2025-05-22 19:56:40 - diamond_sales - ERROR - خطأ في قاعدة البيانات (session_id: 2509109641632): (sqlite3.OperationalError) no such table: cash_box
[SQL: SELECT cash_box.id AS cash_box_id, cash_box.balance AS cash_box_balance, cash_box.last_updated AS cash_box_last_updated 
FROM cash_box
 LIMIT ? OFFSET ?]
[parameters: (1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8): (sqlite3.OperationalError) no such table: cash_box
[SQL: SELECT cash_box.id AS cash_box_id, cash_box.balance AS cash_box_balance, cash_box.last_updated AS cash_box_last_updated 
FROM cash_box
 LIMIT ? OFFSET ?]
[parameters: (1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: cash_box

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "f:\DiamondSalesV1.30\DiamondSalesV1.20\db_session.py", line 70, in session_scope
    yield session
  File "f:\DiamondSalesV1.30\DiamondSalesV1.20\fix_cash_vouchers.py", line 20, in fix_cash_box_vouchers
    cash_box = session.query(صندوق_النقدية).first()
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ~~~~~~~~~~~~~~~~~~~^^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
        params,
        ^^^^^^^
        execution_options={"_sa_orm_load_options": self.load_options},
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: cash_box
[SQL: SELECT cash_box.id AS cash_box_id, cash_box.balance AS cash_box_balance, cash_box.last_updated AS cash_box_last_updated 
FROM cash_box
 LIMIT ? OFFSET ?]
[parameters: (1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-22 20:02:29 - diamond_sales - INFO - تم فتح اتصال جديد بقاعدة البيانات (ID: 1275667546416)
2025-05-22 20:02:29 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1275667546416)
2025-05-22 20:02:29 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1275667546416)
2025-05-22 20:02:29 - diamond_sales - ERROR - خطأ في قاعدة البيانات (session_id: 1275665449376): (sqlite3.OperationalError) no such table: cash_box
[SQL: SELECT cash_box.id AS cash_box_id, cash_box.balance AS cash_box_balance, cash_box.last_updated AS cash_box_last_updated 
FROM cash_box
 LIMIT ? OFFSET ?]
[parameters: (1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8): (sqlite3.OperationalError) no such table: cash_box
[SQL: SELECT cash_box.id AS cash_box_id, cash_box.balance AS cash_box_balance, cash_box.last_updated AS cash_box_last_updated 
FROM cash_box
 LIMIT ? OFFSET ?]
[parameters: (1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: cash_box

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "f:\DiamondSalesV1.30\DiamondSalesV1.20\db_session.py", line 70, in session_scope
    yield session
  File "f:\DiamondSalesV1.30\DiamondSalesV1.20\fix_cash_vouchers.py", line 20, in fix_cash_box_vouchers
    cash_box = session.query(صندوق_النقدية).first()
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ~~~~~~~~~~~~~~~~~~~^^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
        params,
        ^^^^^^^
        execution_options={"_sa_orm_load_options": self.load_options},
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "F:\DiamondSalesV1.30\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: cash_box
[SQL: SELECT cash_box.id AS cash_box_id, cash_box.balance AS cash_box_balance, cash_box.last_updated AS cash_box_last_updated 
FROM cash_box
 LIMIT ? OFFSET ?]
[parameters: (1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-23 14:29:41 - diamond_sales - INFO - بدء تشغيل التطبيق
2025-05-23 14:29:41 - diamond_sales - INFO - تهيئة قاعدة البيانات
2025-05-23 14:29:42 - diamond_sales - INFO - تم تحديث هيكل قاعدة البيانات بنجاح
2025-05-23 14:29:42 - diamond_sales - INFO - إنشاء إعدادات افتراضية جديدة
2025-05-23 14:29:42 - diamond_sales - INFO - إنشاء صندوق النقدية الافتراضي
2025-05-23 14:29:42 - diamond_sales - INFO - إنشاء حساب المسؤول الافتراضي
2025-05-23 14:29:42 - diamond_sales - INFO - إنشاء صلاحية: إدارة المستخدمين
2025-05-23 14:29:42 - diamond_sales - INFO - إنشاء صلاحية: إدارة العملاء
2025-05-23 14:29:42 - diamond_sales - INFO - إنشاء صلاحية: إدارة الموردين
2025-05-23 14:29:42 - diamond_sales - INFO - إنشاء صلاحية: إدارة المبيعات
2025-05-23 14:29:42 - diamond_sales - INFO - إنشاء صلاحية: إدارة المشتريات
2025-05-23 14:29:42 - diamond_sales - INFO - إنشاء صلاحية: إدارة التقارير
2025-05-23 14:29:42 - diamond_sales - INFO - إنشاء صلاحية: إدارة الإعدادات
2025-05-23 14:29:42 - diamond_sales - INFO - إنشاء صلاحية: إدارة النسخ الاحتياطي
2025-05-23 14:29:43 - diamond_sales - INFO - إنشاء صلاحية: إقفال البيانات
2025-05-23 14:29:43 - diamond_sales - INFO - إنشاء صلاحية: إدارة الصلاحيات
2025-05-23 14:29:43 - diamond_sales - INFO - إنشاء صلاحية: إدارة صندوق النقدية
2025-05-23 14:29:43 - diamond_sales - INFO - إنشاء صلاحية: عرض صندوق النقدية
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة المستخدمين' لدور المسؤول
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة العملاء' لدور المسؤول
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة الموردين' لدور المسؤول
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة المبيعات' لدور المسؤول
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة المشتريات' لدور المسؤول
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة التقارير' لدور المسؤول
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة الإعدادات' لدور المسؤول
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة النسخ الاحتياطي' لدور المسؤول
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إقفال البيانات' لدور المسؤول
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة الصلاحيات' لدور المسؤول
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة صندوق النقدية' لدور المسؤول
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'عرض صندوق النقدية' لدور المسؤول
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة العملاء' لدور user
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة المبيعات' لدور user
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة التقارير' لدور user
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'عرض صندوق النقدية' لدور user
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة العملاء' لدور accountant
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة الموردين' لدور accountant
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة المبيعات' لدور accountant
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة المشتريات' لدور accountant
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة التقارير' لدور accountant
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة صندوق النقدية' لدور accountant
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'عرض صندوق النقدية' لدور accountant
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة العملاء' لدور sales
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة المبيعات' لدور sales
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة التقارير' لدور sales
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'عرض صندوق النقدية' لدور sales
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة العملاء' لدور manager
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة الموردين' لدور manager
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة المبيعات' لدور manager
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة المشتريات' لدور manager
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة التقارير' لدور manager
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة المستخدمين' لدور manager
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'إدارة صندوق النقدية' لدور manager
2025-05-23 14:29:43 - diamond_sales - INFO - إضافة صلاحية 'عرض صندوق النقدية' لدور manager
2025-05-23 14:29:43 - diamond_sales - INFO - إنشاء فهارس لتحسين الأداء
2025-05-23 14:29:43 - diamond_sales - INFO - تم تهيئة قاعدة البيانات بنجاح
2025-05-23 14:29:43 - diamond_sales - INFO - تهيئة حالة التطبيق
2025-05-23 14:29:43 - diamond_sales - INFO - تم تهيئة حالة التطبيق بنجاح
2025-05-23 14:29:44 - diamond_sales - INFO - تم فتح اتصال جديد بقاعدة البيانات (ID: 2461658646944)
2025-05-23 14:29:44 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 2461658646944)
2025-05-23 14:29:44 - diamond_sales - INFO - تم تطبيق الخط: Arial، الحجم: 10
2025-05-23 14:29:44 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 2461658646944)
2025-05-23 14:29:44 - diamond_sales - INFO - تحميل الترجمات للغة: ar
2025-05-23 14:29:44 - diamond_sales - INFO - البرنامج غير مفعل، تهيئة نافذة التفعيل
2025-05-23 14:29:45 - diamond_sales - INFO - عرض نافذة التفعيل
2025-05-23 14:30:22 - diamond_sales - INFO - تم تفعيل البرنامج بنجاح - معرف الجهاز: 4388DDDEF3B14B93
2025-05-23 14:30:59 - diamond_sales - INFO - بدء تشغيل التطبيق
2025-05-23 14:30:59 - diamond_sales - INFO - تهيئة قاعدة البيانات
2025-05-23 14:30:59 - diamond_sales - INFO - تم تحديث هيكل قاعدة البيانات بنجاح
2025-05-23 14:30:59 - diamond_sales - INFO - إنشاء فهارس لتحسين الأداء
2025-05-23 14:30:59 - diamond_sales - INFO - تم تهيئة قاعدة البيانات بنجاح
2025-05-23 14:30:59 - diamond_sales - INFO - تهيئة حالة التطبيق
2025-05-23 14:30:59 - diamond_sales - INFO - تم تهيئة حالة التطبيق بنجاح
2025-05-23 14:31:01 - diamond_sales - INFO - تم فتح اتصال جديد بقاعدة البيانات (ID: 1650861868272)
2025-05-23 14:31:01 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:01 - diamond_sales - INFO - تم تطبيق الخط: Arial، الحجم: 10
2025-05-23 14:31:01 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:01 - diamond_sales - INFO - تحميل الترجمات للغة: ar
2025-05-23 14:31:01 - diamond_sales - INFO - البرنامج مفعل، تهيئة نافذة تسجيل الدخول
2025-05-23 14:31:01 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:01 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:01 - diamond_sales - INFO - تم تحميل قائمة المستخدمين بنجاح
2025-05-23 14:31:02 - diamond_sales - INFO - عرض نافذة تسجيل الدخول
2025-05-23 14:31:22 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:22 - diamond_sales.login - INFO - محاولة تسجيل دخول (نجاح) - المستخدم: admin من 192.168.2.24
2025-05-23 14:31:22 - diamond_sales - INFO - تم تسجيل دخول المستخدم admin بنجاح
2025-05-23 14:31:22 - diamond_sales - INFO - تم حفظ بيانات تذكر المستخدم admin
2025-05-23 14:31:22 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:22 - diamond_sales - INFO - تم تعيين المستخدم الحالي: admin
2025-05-23 14:31:25 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:25 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:25 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:25 - diamond_sales - INFO - تم تحميل 0 عميل في القائمة المنسدلة
2025-05-23 14:31:25 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:25 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:25 - diamond_sales - INFO - تم تحميل 0 صنف في القائمة المنسدلة
2025-05-23 14:31:25 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:25 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:25 - diamond_sales - INFO - تم تحميل 0 وحدة في القائمة المنسدلة
2025-05-23 14:31:25 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:25 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:25 - diamond_sales - INFO - تم تحميل 0 عملية بيع في الجدول
2025-05-23 14:31:25 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:27 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:27 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:27 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:27 - diamond_sales - INFO - تم تحميل 0 صنف في القائمة المنسدلة
2025-05-23 14:31:27 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:27 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:27 - diamond_sales - INFO - تم تحميل 0 وحدة في القائمة المنسدلة
2025-05-23 14:31:27 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:33 - diamond_sales - INFO - خيارات نوع السند المتاحة:
2025-05-23 14:31:33 - diamond_sales - INFO -   0: الكل
2025-05-23 14:31:33 - diamond_sales - INFO -   1: قبض
2025-05-23 14:31:33 - diamond_sales - INFO -   2: صرف
2025-05-23 14:31:33 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:33 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:33 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:33 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:33 - diamond_sales - INFO - بدء تحميل بيانات السندات
2025-05-23 14:31:33 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:33 - diamond_sales - INFO - عدد السندات المسترجعة من قاعدة البيانات: 0
2025-05-23 14:31:33 - diamond_sales - INFO - عدد السندات بعد المعالجة: 0
2025-05-23 14:31:33 - diamond_sales - INFO - تم تحميل 0 سند في الجدول
2025-05-23 14:31:33 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:36 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:36 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:47 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:47 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:47 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 1650861868272)
2025-05-23 14:31:47 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 1650861868272)
2025-05-23 14:31:49 - diamond_sales - ERROR - استثناء غير معالج: Traceback (most recent call last):
  File "f:\DiamondSalesV1.30\DiamondSalesV1.20\dashboard.py", line 228, in open_reports_screen
    from reports_screen import ReportsScreen
  File "f:\DiamondSalesV1.30\DiamondSalesV1.20\reports_screen.py", line 3742
    total_count += 1                if voucher.receipt_type == "CashIn":
                                                                       ^
SyntaxError: invalid syntax

2025-05-23 14:31:51 - diamond_sales - ERROR - استثناء غير معالج: Traceback (most recent call last):
  File "f:\DiamondSalesV1.30\DiamondSalesV1.20\dashboard.py", line 228, in open_reports_screen
    from reports_screen import ReportsScreen
  File "f:\DiamondSalesV1.30\DiamondSalesV1.20\reports_screen.py", line 3742
    total_count += 1                if voucher.receipt_type == "CashIn":
                                                                       ^
SyntaxError: invalid syntax

2025-05-23 14:31:56 - diamond_sales - ERROR - استثناء غير معالج: Traceback (most recent call last):
  File "f:\DiamondSalesV1.30\DiamondSalesV1.20\dashboard.py", line 228, in open_reports_screen
    from reports_screen import ReportsScreen
  File "f:\DiamondSalesV1.30\DiamondSalesV1.20\reports_screen.py", line 3742
    total_count += 1                if voucher.receipt_type == "CashIn":
                                                                       ^
SyntaxError: invalid syntax

2025-05-23 15:48:09 - diamond_sales - INFO - بدء تشغيل التطبيق
2025-05-23 15:48:09 - diamond_sales - INFO - تهيئة قاعدة البيانات
2025-05-23 15:48:09 - diamond_sales - INFO - تم تحديث هيكل قاعدة البيانات بنجاح
2025-05-23 15:48:09 - diamond_sales - INFO - إنشاء فهارس لتحسين الأداء
2025-05-23 15:48:09 - diamond_sales - INFO - تم تهيئة قاعدة البيانات بنجاح
2025-05-23 15:48:09 - diamond_sales - INFO - تهيئة حالة التطبيق
2025-05-23 15:48:09 - diamond_sales - INFO - تم تهيئة حالة التطبيق بنجاح
2025-05-23 15:48:10 - diamond_sales - INFO - تم فتح اتصال جديد بقاعدة البيانات (ID: 2488478793488)
2025-05-23 15:48:10 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 2488478793488)
2025-05-23 15:48:10 - diamond_sales - INFO - تم تطبيق الخط: Arial، الحجم: 10
2025-05-23 15:48:10 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 2488478793488)
2025-05-23 15:48:10 - diamond_sales - INFO - تحميل الترجمات للغة: ar
2025-05-23 15:48:10 - diamond_sales - INFO - البرنامج مفعل، تهيئة نافذة تسجيل الدخول
2025-05-23 15:48:10 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 2488478793488)
2025-05-23 15:48:10 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 2488478793488)
2025-05-23 15:48:10 - diamond_sales - INFO - تم تحميل قائمة المستخدمين بنجاح
2025-05-23 15:48:10 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 2488478793488)
2025-05-23 15:48:10 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 2488478793488)
2025-05-23 15:48:12 - diamond_sales - INFO - عرض نافذة تسجيل الدخول
2025-05-23 15:48:25 - diamond_sales - INFO - تم استخدام اتصال من التجمع (ID: 2488478793488)
2025-05-23 15:48:25 - diamond_sales.login - INFO - محاولة تسجيل دخول (نجاح) - المستخدم: admin من 192.168.2.24
2025-05-23 15:48:25 - diamond_sales - INFO - تم تسجيل دخول المستخدم admin بنجاح
2025-05-23 15:48:25 - diamond_sales - INFO - تم حفظ بيانات تذكر المستخدم admin
2025-05-23 15:48:25 - diamond_sales - INFO - تم إرجاع اتصال إلى التجمع (ID: 2488478793488)
2025-05-23 15:48:25 - diamond_sales - INFO - تم تعيين المستخدم الحالي: admin
