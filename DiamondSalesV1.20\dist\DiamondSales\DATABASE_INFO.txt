===============================================
معلومات قاعدة البيانات - Diamond Sales
===============================================

📊 حالة قاعدة البيانات: نظيفة ومُعدة للاستخدام
🗓️ تاريخ التحضير: مايو 2025
🔧 إصدار قاعدة البيانات: 1.30

===============================================
🏗️ هيكل قاعدة البيانات:
===============================================

📋 الجداول الرئيسية:

👥 جدول العملاء (customers):
- معرف العميل
- الاسم الكامل
- رقم الهوية
- رقم الهاتف
- العنوان

🏭 جدول الموردين (suppliers):
- معرف المورد
- اسم الشركة
- رقم السجل التجاري
- رقم الهاتف
- العنوان

💎 جدول المبيعات (sales):
- معرف العملية
- معرف العميل
- نوع الألماس
- الوزن بالقيراط
- السعر للقيراط (دولار)
- إجمالي السعر (دولار/ريال)
- تاريخ البيع
- سعر الصرف
- المبلغ المستحق
- المبلغ المدفوع
- الملاحظات

🛒 جدول المشتريات (purchases):
- معرف العملية
- معرف المورد
- نوع الألماس
- الوزن بالقيراط
- السعر للقيراط (دولار)
- إجمالي السعر (دولار/ريال)
- تاريخ الشراء
- سعر الصرف
- المبلغ المستحق
- المبلغ المدفوع

📄 جدول السندات (receipts):
- معرف السند
- نوع السند (قبض/صرف)
- معرف العملية المرتبطة
- معرف العميل/المورد
- المبلغ (دولار/ريال)
- تاريخ الإصدار

💰 جدول صندوق النقدية (cash_box):
- معرف الصندوق
- الرصيد الحالي
- تاريخ آخر تحديث

💸 جدول حركات النقدية (cash_transactions):
- معرف الحركة
- معرف الصندوق
- نوع الحركة (إيداع/سحب)
- المبلغ
- الرصيد بعد العملية
- تاريخ الحركة
- المرجع
- الوصف
- معرف المستخدم

👤 جدول المستخدمين (users):
- معرف المستخدم
- اسم المستخدم
- كلمة المرور المشفرة
- الدور (admin/user/accountant/sales/manager)
- الاسم الكامل
- البريد الإلكتروني
- رقم الهاتف
- حالة النشاط
- تاريخ آخر دخول
- تاريخ الإنشاء

⚙️ جدول الإعدادات (settings):
- سعر الصرف
- العملة الافتراضية
- اللغة
- مسار النسخ الاحتياطي
- تاريخ آخر نسخة احتياطية
- النسخ الاحتياطي التلقائي
- نوع الخط
- حجم الخط

🏢 جدول معلومات الشركة (company_info):
- اسم الشركة
- العنوان
- الرقم الضريبي
- مسار الشعار

📊 جدول الأصناف (categories):
- معرف الصنف
- اسم الصنف
- الوصف

📏 جدول الوحدات (units):
- معرف الوحدة
- اسم الوحدة
- الرمز
- الوصف
- معرف الصنف المرتبط

🔐 جدول الصلاحيات (permissions):
- معرف الصلاحية
- اسم الصلاحية
- الوصف
- الكود

🔗 جدول ربط الأدوار بالصلاحيات (role_permissions):
- معرف الربط
- الدور
- معرف الصلاحية

🔒 جدول إقفال البيانات (data_locks):
- معرف القفل
- نوع فترة القفل
- تاريخ البداية
- تاريخ النهاية
- حالة القفل
- معرف المستخدم المنشئ

🎫 جدول التفعيل (activation):
- معرف التفعيل
- كود التفعيل
- تاريخ التفعيل
- تاريخ انتهاء الصلاحية
- معرف الجهاز
- الميزات المفعلة

===============================================
📊 البيانات الافتراضية المُحملة:
===============================================

👤 المستخدم الافتراضي:
- اسم المستخدم: admin
- كلمة المرور: 1
- الدور: مدير النظام (admin)

⚙️ الإعدادات الافتراضية:
- سعر الصرف: 3.75 ريال للدولار
- العملة الافتراضية: دولار أمريكي (USD)
- اللغة: العربية (ar)
- النسخ الاحتياطي التلقائي: مفعل

🏢 معلومات الشركة الافتراضية:
- اسم الشركة: شركة تجارة الألماس
- العنوان: (فارغ - يحتاج تعديل)
- الرقم الضريبي: (فارغ - يحتاج تعديل)

===============================================
🔧 صيانة قاعدة البيانات:
===============================================

✅ العمليات المُنجزة:
- تفريغ جميع البيانات التشغيلية
- الاحتفاظ بالهيكل والبيانات الأساسية
- إعادة تعيين معرفات التسلسل التلقائي
- التحقق من سلامة البيانات

🧹 البيانات المحذوفة:
- جميع بيانات العملاء والموردين
- جميع عمليات المبيعات والمشتريات
- جميع السندات والحركات النقدية
- جميع المدفوعات والقيود المحاسبية
- جميع أقفال البيانات

🔒 البيانات المحفوظة:
- المستخدم الافتراضي (admin)
- الإعدادات الأساسية
- معلومات الشركة الافتراضية
- هيكل قاعدة البيانات
- الصلاحيات والأدوار

===============================================
⚠️ تحذيرات مهمة:
===============================================

🚫 لا تقم بـ:
- تعديل ملف قاعدة البيانات يدوياً
- حذف أو نقل ملف diamond_sales.db
- تشغيل أكثر من نسخة من البرنامج
- إيقاف البرنامج أثناء حفظ البيانات

✅ يُنصح بـ:
- عمل نسخة احتياطية دورية
- مراقبة حجم قاعدة البيانات
- تنظيف البيانات القديمة دورياً
- التحقق من سلامة البيانات

===============================================
🔄 النسخ الاحتياطي:
===============================================

📅 التكرار المُوصى به:
- يومياً: للبيانات الحرجة
- أسبوعياً: للاستخدام العادي
- شهرياً: كحد أدنى

📁 مكان الحفظ:
- قرص صلب خارجي
- خدمة تخزين سحابية
- خادم شبكة محلية

🔧 طريقة الاستعادة:
- استخدم خاصية "استعادة من نسخة احتياطية"
- أو استبدل ملف diamond_sales.db
- أعد تشغيل البرنامج

===============================================
📞 الدعم الفني:
===============================================

في حالة مشاكل قاعدة البيانات:
📧 <EMAIL>
🌐 www.diamondsales.com/support

===============================================
