('F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\diamond_sales\\DiamondSales.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\diamond_sales\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\diamond_sales\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\diamond_sales\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\diamond_sales\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\diamond_sales\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\diamond_sales\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('main', 'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\main.py', 'PYSOURCE')],
 'python313.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
