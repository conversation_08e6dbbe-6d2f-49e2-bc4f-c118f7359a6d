import os
import re

def fix_reports_screen():
    # Read the original file
    with open('reports_screen.py', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Fix docstrings
    content = re.sub(r'def ([^(]+)\([^)]*\):\s*"""([^"]+)"""', r'def \1():\n        # \2', content)
    
    # Fix f-strings
    # This is a simplified approach - in a real scenario, you'd need more sophisticated parsing
    
    # Example of fixing a specific f-string section
    html_pattern = r'html = f"""<!DOCTYPE html>(.*?)</div>"""'
    html_replacement = '''html = "<!DOCTYPE html>"
            html += '<html dir="rtl" lang="ar">'
            html += '<head>'
            html += '<meta charset="UTF-8">'
            html += '<title>تقرير حركة الأصناف</title>'
            html += '<style>'
            html += "body { font-family: 'Arial', sans-serif; margin: 20px; direction: rtl; }"
            html += ".header { text-align: center; margin-bottom: 20px; }"
            html += ".title { font-size: 24px; font-weight: bold; }"
            html += ".subtitle { font-size: 18px; margin: 10px 0; }"
            html += "table { width: 100%; border-collapse: collapse; margin: 15px 0; }"
            html += "th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }"
            html += "th { background-color: #f2f2f2; }"
            html += "tr:nth-child(even) { background-color: #f9f9f9; }"
            html += ".summary { margin-top: 20px; font-weight: bold; }"
            html += ".footer { margin-top: 30px; text-align: center; font-size: 12px; color: #777; }"
            html += '</style>'
            html += '</head>'
            html += '<body>'
            html += '<div class="header">'
            html += f'<div class="title">{company_name}</div>'
            html += '<div class="subtitle">تقرير حركة الأصناف</div>'
            html += f'<div>للفترة من {start_date} إلى {end_date}</div>'
            html += '</div>'
'''
    content = re.sub(html_pattern, html_replacement, content, flags=re.DOTALL)
    
    # Fix specific problematic lines
    content = content.replace('<th>{avg_price_per_carat:.3f}</th>', '<th>{format(avg_price_per_carat, ".3f")}</th>')
    content = content.replace('<th>{total_price_sar_total:.3f}</th>', '<th>{format(total_price_sar_total, ".3f")}</th>')
    content = content.replace('<th>{total_amount_paid_sar:.3f}</th>', '<th>{format(total_amount_paid_sar, ".3f")}</th>')
    content = content.replace('<th>{total_balance_sar:.3f}</th>', '<th>{format(total_balance_sar, ".3f")}</th>')
    
    # Write the fixed content to a new file
    with open('reports_screen_fixed.py', 'w', encoding='utf-8') as file:
        file.write(content)
    
    print("Fixed file saved as reports_screen_fixed.py")
    print("To use the fixed file, rename it to reports_screen.py")

if __name__ == "__main__":
    fix_reports_screen()
