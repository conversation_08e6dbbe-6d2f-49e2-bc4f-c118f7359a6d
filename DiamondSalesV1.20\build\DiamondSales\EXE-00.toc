('F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\dist\\DiamondSales.exe',
 <PERSON><PERSON><PERSON>,
 <PERSON><PERSON><PERSON>,
 <PERSON>alse,
 ['F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\diamond_icon.ico'],
 None,
 <PERSON>als<PERSON>,
 <PERSON>als<PERSON>,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\DiamondSales\\DiamondSales.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\DiamondSales\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\DiamondSales\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\DiamondSales\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\DiamondSales\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\DiamondSales\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\DiamondSales\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('main', 'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\main.py', 'PYSOURCE'),
  ('python313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\sip.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\util.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\processors.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\collections.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\greenlet\\_greenlet.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('PyQt6\\QtPrintSupport.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python3.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6PrintSupport.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6PrintSupport.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin\\ucrtbase.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('assets\\add_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\add_icon.ico',
   'DATA'),
  ('assets\\add_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\add_icon.svg',
   'DATA'),
  ('assets\\cancel_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\cancel_icon.ico',
   'DATA'),
  ('assets\\cancel_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\cancel_icon.svg',
   'DATA'),
  ('assets\\customers_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\customers_icon.ico',
   'DATA'),
  ('assets\\customers_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\customers_icon.svg',
   'DATA'),
  ('assets\\delete_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\delete_icon.ico',
   'DATA'),
  ('assets\\delete_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\delete_icon.svg',
   'DATA'),
  ('assets\\diamond_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\diamond_icon.ico',
   'DATA'),
  ('assets\\diamond_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\diamond_icon.svg',
   'DATA'),
  ('assets\\edit_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\edit_icon.ico',
   'DATA'),
  ('assets\\edit_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\edit_icon.svg',
   'DATA'),
  ('assets\\export_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\export_icon.ico',
   'DATA'),
  ('assets\\export_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\export_icon.svg',
   'DATA'),
  ('assets\\eye_closed.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_closed.ico',
   'DATA'),
  ('assets\\eye_closed.png',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_closed.png',
   'DATA'),
  ('assets\\eye_closed.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_closed.svg',
   'DATA'),
  ('assets\\eye_open.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_open.ico',
   'DATA'),
  ('assets\\eye_open.png',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_open.png',
   'DATA'),
  ('assets\\eye_open.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\eye_open.svg',
   'DATA'),
  ('assets\\import_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\import_icon.ico',
   'DATA'),
  ('assets\\import_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\import_icon.svg',
   'DATA'),
  ('assets\\inventory_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\inventory_icon.ico',
   'DATA'),
  ('assets\\inventory_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\inventory_icon.svg',
   'DATA'),
  ('assets\\login_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\login_icon.ico',
   'DATA'),
  ('assets\\login_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\login_icon.svg',
   'DATA'),
  ('assets\\logout_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\logout_icon.ico',
   'DATA'),
  ('assets\\logout_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\logout_icon.svg',
   'DATA'),
  ('assets\\print_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\print_icon.ico',
   'DATA'),
  ('assets\\print_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\print_icon.svg',
   'DATA'),
  ('assets\\purchases_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\purchases_icon.ico',
   'DATA'),
  ('assets\\purchases_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\purchases_icon.svg',
   'DATA'),
  ('assets\\reports_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\reports_icon.ico',
   'DATA'),
  ('assets\\reports_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\reports_icon.svg',
   'DATA'),
  ('assets\\sales_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\sales_icon.ico',
   'DATA'),
  ('assets\\sales_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\sales_icon.svg',
   'DATA'),
  ('assets\\save_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\save_icon.ico',
   'DATA'),
  ('assets\\save_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\save_icon.svg',
   'DATA'),
  ('assets\\search_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\search_icon.ico',
   'DATA'),
  ('assets\\search_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\search_icon.svg',
   'DATA'),
  ('assets\\settings_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\settings_icon.ico',
   'DATA'),
  ('assets\\settings_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\settings_icon.svg',
   'DATA'),
  ('assets\\suppliers_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\suppliers_icon.ico',
   'DATA'),
  ('assets\\suppliers_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\suppliers_icon.svg',
   'DATA'),
  ('assets\\users_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\users_icon.ico',
   'DATA'),
  ('assets\\users_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\users_icon.svg',
   'DATA'),
  ('assets\\vouchers_icon.ico',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\vouchers_icon.ico',
   'DATA'),
  ('assets\\vouchers_icon.svg',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\assets\\vouchers_icon.svg',
   'DATA'),
  ('translations\\__init__.py',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__init__.py',
   'DATA'),
  ('translations\\__pycache__\\__init__.cpython-311.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('translations\\__pycache__\\__init__.cpython-313.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('translations\\__pycache__\\ar.cpython-311.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\ar.cpython-311.pyc',
   'DATA'),
  ('translations\\__pycache__\\ar.cpython-313.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\ar.cpython-313.pyc',
   'DATA'),
  ('translations\\__pycache__\\en.cpython-313.pyc',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\__pycache__\\en.cpython-313.pyc',
   'DATA'),
  ('translations\\ar.py',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\ar.py',
   'DATA'),
  ('translations\\en.py',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\translations\\en.py',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ka.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\build\\DiamondSales\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 **********,
 [('runw.exe',
   'F:\\DiamondSalesV1.30\\DiamondSalesV1.20\\.venv\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll')
