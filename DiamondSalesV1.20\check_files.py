#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت للتحقق من وجود جميع الملفات المطلوبة للمثبت
"""

import os

def check_files():
    """التحقق من وجود جميع الملفات المطلوبة"""
    print("🔍 التحقق من وجود الملفات المطلوبة للمثبت...")
    
    # قائمة الملفات المطلوبة
    required_files = [
        "dist/DiamondSales/DiamondSales.exe",
        "dist/DiamondSales/diamond_sales.db",
        "dist/DiamondSales/README.txt",
        "dist/DiamondSales/دليل_البدء_السريع.txt",
        "dist/DiamondSales/VERSION_INFO.txt",
        "dist/DiamondSales/DATABASE_INFO.txt",
        "assets/diamond_icon.ico"
    ]
    
    # قائمة المجلدات المطلوبة
    required_dirs = [
        "dist/DiamondSales/assets",
        "dist/DiamondSales/translations",
        "dist/DiamondSales/logs",
        "dist/DiamondSales/_internal"
    ]
    
    all_good = True
    
    print("\n📄 فحص الملفات:")
    for file_path in required_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✅ {file_path} ({size:,} بايت)")
        else:
            print(f"  ❌ {file_path} - غير موجود")
            all_good = False
    
    print("\n📁 فحص المجلدات:")
    for dir_path in required_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            files_count = len([f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))])
            print(f"  ✅ {dir_path} ({files_count} ملف)")
        else:
            print(f"  ❌ {dir_path} - غير موجود")
            all_good = False
    
    print("\n" + "="*50)
    if all_good:
        print("✅ جميع الملفات موجودة! يمكن تشغيل المثبت الآن.")
        print("\n📋 خطوات تشغيل المثبت:")
        print("1. تأكد من تثبيت Inno Setup")
        print("2. افتح ملف DiamondSales_Setup.iss")
        print("3. اضغط F9 أو Build > Compile")
        print("4. ستجد المثبت في مجلد installer_output")
    else:
        print("❌ بعض الملفات مفقودة! يرجى إعادة بناء البرنامج.")
        print("\n🔧 لإعادة البناء:")
        print("1. شغل: python build_exe.py")
        print("2. تأكد من نجاح العملية")
        print("3. شغل هذا السكريبت مرة أخرى")
    
    return all_good

def show_installer_info():
    """عرض معلومات المثبت"""
    print("\n📦 معلومات المثبت:")
    print("- اسم الملف: DiamondSalesSetup_v1.30.exe")
    print("- المجلد: installer_output/")
    print("- الحجم المتوقع: ~50-100 MB")
    print("- يدعم: Windows 10/11 (64-bit)")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔍 فحص ملفات Diamond Sales للمثبت")
    print("=" * 60)
    
    if check_files():
        show_installer_info()
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
